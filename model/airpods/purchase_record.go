package airpods

import (
	"airAi/global"
	"time"
)

// PurchaseRecord 配额购买记录
type PurchaseRecord struct {
	global.GVA_MODEL
	Uid           uint      `json:"uid" gorm:"index;comment:用户ID"`                          // 用户ID
	PurchaseType  int       `json:"purchaseType" gorm:"default:1;comment:购买类型 1额外配额 2月度订阅"` // 购买类型
	PurchaseUnits int       `json:"purchaseUnits" gorm:"not null;comment:购买单位数"`            // 购买单位数（50次/单位）
	Amount        float64   `json:"amount" gorm:"not null;comment:购买金额"`                    // 购买金额（RMB）
	PurchaseDate  time.Time `json:"purchaseDate" gorm:"autoCreateTime;comment:购买时间"`        // 购买时间
	Status        int       `json:"status" gorm:"default:1;comment:状态 0待支付 1已支付 2已取消 3已退款"` // 支付状态
	PaymentMethod string    `json:"paymentMethod" gorm:"comment:支付方式"`                      // 支付方式
	OrderID       string    `json:"orderID" gorm:"uniqueIndex;comment:订单ID"`                // 订单ID
	Description   string    `json:"description" gorm:"comment:购买描述"`                        // 购买描述
	ClientIP      string    `json:"clientIP" gorm:"comment:客户端IP"`                          // 客户端IP
}

func (PurchaseRecord) TableName() string {
	return "airpods_purchase_record"
}

// GetPurchaseTypeText 获取购买类型文本
func (p *PurchaseRecord) GetPurchaseTypeText() string {
	switch p.PurchaseType {
	case 1:
		return "额外配额"
	case 2:
		return "月度订阅"
	default:
		return "未知类型"
	}
}

// GetStatusText 获取状态文本
func (p *PurchaseRecord) GetStatusText() string {
	switch p.Status {
	case 0:
		return "待支付"
	case 1:
		return "已支付"
	case 2:
		return "已取消"
	case 3:
		return "已退款"
	default:
		return "未知状态"
	}
}
