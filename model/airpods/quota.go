package airpods

import (
	"airAi/global"
	"time"
)

// Quota 用户配额模型
type Quota struct {
	global.GVA_MODEL
	Uid                uint      `json:"uid" gorm:"uniqueIndex;comment:用户ID"`                        // 用户ID，唯一索引
	MonthlyQuota       int       `json:"monthlyQuota" gorm:"default:300;comment:每月基础配额"`             // 每月基础配额（默认300次）
	CurrentUsage       int       `json:"currentUsage" gorm:"default:0;comment:当前月使用量"`               // 当前月使用量
	ExtraPurchases     int       `json:"extraPurchases" gorm:"default:0;comment:额外购买的配额"`            // 额外购买的配额
	LastResetDate      time.Time `json:"lastResetDate" gorm:"comment:最后重置日期"`                        // 最后重置日期
	SubscriptionStatus int       `json:"subscriptionStatus" gorm:"default:0;comment:订阅状态 0未订阅 1已订阅"` // 订阅状态
	SubscriptionExpiry time.Time `json:"subscriptionExpiry" gorm:"comment:订阅到期时间"`                   // 订阅到期时间
	IsActive           bool      `json:"isActive" gorm:"default:true;comment:配额是否激活"`                // 配额是否激活
}

func (Quota) TableName() string {
	return "airpods_quota"
}

// GetTotalQuota 获取总配额（基础配额 + 额外购买）
func (q *Quota) GetTotalQuota() int {
	baseQuota := 0
	if q.SubscriptionStatus == 1 && q.SubscriptionExpiry.After(time.Now()) {
		baseQuota = q.MonthlyQuota
	}
	return baseQuota + q.ExtraPurchases
}

// GetRemainingQuota 获取剩余配额
func (q *Quota) GetRemainingQuota() int {
	total := q.GetTotalQuota()
	remaining := total - q.CurrentUsage
	if remaining < 0 {
		return 0
	}
	return remaining
}

// CanUse 检查是否可以使用（剩余配额大于0且配额激活）
func (q *Quota) CanUse() bool {
	return q.IsActive && q.GetRemainingQuota() > 0
}

// IsSubscriptionActive 检查订阅是否有效
func (q *Quota) IsSubscriptionActive() bool {
	return q.SubscriptionStatus == 1 && q.SubscriptionExpiry.After(time.Now())
}
