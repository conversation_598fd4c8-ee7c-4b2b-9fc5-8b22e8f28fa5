package airpods

import (
	"airAi/global"
	"time"
)

// QuotaUsageLog 配额使用日志
type QuotaUsageLog struct {
	global.GVA_MODEL
	Uid           uint      `json:"uid" gorm:"index;comment:用户ID"`                                    // 用户ID
	UsageType     int       `json:"usageType" gorm:"default:1;comment:使用类型 1聊天 2语音识别 3文字转语音"`       // 使用类型
	RequestPath   string    `json:"requestPath" gorm:"comment:请求路径"`                                 // 请求路径
	RequestMethod string    `json:"requestMethod" gorm:"comment:请求方法"`                               // 请求方法
	ClientIP      string    `json:"clientIP" gorm:"comment:客户端IP"`                                  // 客户端IP
	UserAgent     string    `json:"userAgent" gorm:"comment:用户代理"`                                  // 用户代理
	UsageDate     time.Time `json:"usageDate" gorm:"autoCreateTime;comment:使用时间"`                   // 使用时间
	Success       bool      `json:"success" gorm:"default:true;comment:是否成功"`                       // 是否成功
	ErrorMessage  string    `json:"errorMessage" gorm:"comment:错误信息"`                               // 错误信息
	ResponseTime  int64     `json:"responseTime" gorm:"comment:响应时间(毫秒)"`                           // 响应时间（毫秒）
	ConversationID string   `json:"conversationID" gorm:"comment:对话ID"`                             // 对话ID（聊天时使用）
	RequestSize   int       `json:"requestSize" gorm:"comment:请求大小(字节)"`                            // 请求大小（字节）
	ResponseSize  int       `json:"responseSize" gorm:"comment:响应大小(字节)"`                           // 响应大小（字节）
}

func (QuotaUsageLog) TableName() string {
	return "airpods_quota_usage_log"
}

// GetUsageTypeText 获取使用类型文本
func (q *QuotaUsageLog) GetUsageTypeText() string {
	switch q.UsageType {
	case 1:
		return "聊天"
	case 2:
		return "语音识别"
	case 3:
		return "文字转语音"
	default:
		return "未知类型"
	}
}

// GetSuccessText 获取成功状态文本
func (q *QuotaUsageLog) GetSuccessText() string {
	if q.Success {
		return "成功"
	}
	return "失败"
}
