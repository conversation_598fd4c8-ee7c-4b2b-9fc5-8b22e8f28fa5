#!/bin/bash

# 测试多轮对话上下文功能
# 验证AI是否能记住之前的对话内容

echo "=== airAi 多轮对话上下文测试 ==="
echo ""

# 服务器地址
SERVER_URL="http://localhost:8888"

# 测试1：HTTP API 多轮对话测试
echo "🔍 测试1：HTTP API 多轮对话上下文"
echo "----------------------------------------"

# 第一轮对话：介绍自己
echo "第一轮：介绍自己"
RESPONSE1=$(curl -s -X POST "$SERVER_URL/v1/syncChat" \
  -H "Content-Type: application/json" \
  -H "Accept-Language: zh_CN" \
  -d '{"text": "我的名字是张三，我是一名软件工程师"}')

echo "用户：我的名字是张三，我是一名软件工程师"
echo "AI响应：$(echo $RESPONSE1 | jq -r '.data.info // .msg')"

# 提取conversation_id
CONVERSATION_ID=$(echo $RESPONSE1 | jq -r '.data.conversation_id // ""')
echo "对话ID：$CONVERSATION_ID"
echo ""

# 第二轮对话：询问名字（测试上下文记忆）
echo "第二轮：询问名字（测试上下文记忆）"
RESPONSE2=$(curl -s -X POST "$SERVER_URL/v1/syncChat" \
  -H "Content-Type: application/json" \
  -H "Accept-Language: zh_CN" \
  -d "{\"text\": \"我的名字是什么？\", \"conversation_id\": \"$CONVERSATION_ID\"}")

echo "用户：我的名字是什么？"
echo "AI响应：$(echo $RESPONSE2 | jq -r '.data.info // .msg')"
echo ""

# 第三轮对话：询问职业（测试上下文记忆）
echo "第三轮：询问职业（测试上下文记忆）"
RESPONSE3=$(curl -s -X POST "$SERVER_URL/v1/syncChat" \
  -H "Content-Type: application/json" \
  -H "Accept-Language: zh_CN" \
  -d "{\"text\": \"我的职业是什么？\", \"conversation_id\": \"$CONVERSATION_ID\"}")

echo "用户：我的职业是什么？"
echo "AI响应：$(echo $RESPONSE3 | jq -r '.data.info // .msg')"
echo ""

# 测试2：多语言对话上下文测试
echo "🌍 测试2：多语言对话上下文"
echo "----------------------------------------"

# 英文对话
echo "英文对话测试："
EN_RESPONSE1=$(curl -s -X POST "$SERVER_URL/v1/syncChat" \
  -H "Content-Type: application/json" \
  -H "Accept-Language: en" \
  -d '{"text": "My name is John and I am a teacher"}')

EN_CONVERSATION_ID=$(echo $EN_RESPONSE1 | jq -r '.data.conversation_id // ""')
echo "用户：My name is John and I am a teacher"
echo "AI响应：$(echo $EN_RESPONSE1 | jq -r '.data.info // .msg')"
echo "对话ID：$EN_CONVERSATION_ID"

EN_RESPONSE2=$(curl -s -X POST "$SERVER_URL/v1/syncChat" \
  -H "Content-Type: application/json" \
  -H "Accept-Language: en" \
  -d "{\"text\": \"What is my name?\", \"conversation_id\": \"$EN_CONVERSATION_ID\"}")

echo "用户：What is my name?"
echo "AI响应：$(echo $EN_RESPONSE2 | jq -r '.data.info // .msg')"
echo ""

# 测试3：WebSocket 多轮对话测试
echo "🔌 测试3：WebSocket 多轮对话上下文"
echo "----------------------------------------"

# 创建WebSocket测试脚本
cat > test_websocket_context.js << 'EOF'
const WebSocket = require('ws');

const ws = new WebSocket('ws://localhost:8888/v1/chat');

let conversationId = '';
let testStep = 0;

const tests = [
    {
        type: "chat",
        content: "我叫李四，我喜欢编程",
        stream: false,
        language: "zh_CN"
    },
    {
        type: "chat", 
        content: "我的名字是什么？",
        stream: false,
        language: "zh_CN"
    },
    {
        type: "chat",
        content: "我喜欢什么？", 
        stream: false,
        language: "zh_CN"
    }
];

ws.on('open', function open() {
    console.log('WebSocket连接已建立');
    runNextTest();
});

ws.on('message', function message(data) {
    const response = JSON.parse(data);
    console.log(`\n步骤 ${testStep}: ${tests[testStep-1].content}`);
    console.log(`AI响应: ${response.content}`);
    
    if (response.conversation_id) {
        conversationId = response.conversation_id;
        console.log(`对话ID: ${conversationId}`);
    }
    
    if (response.type === 'complete') {
        setTimeout(runNextTest, 1000);
    }
});

ws.on('error', function error(err) {
    console.error('WebSocket错误:', err);
});

function runNextTest() {
    if (testStep >= tests.length) {
        console.log('\n✅ WebSocket测试完成');
        ws.close();
        return;
    }
    
    const test = tests[testStep];
    if (conversationId) {
        test.conversation_id = conversationId;
    }
    
    testStep++;
    console.log(`\n发送消息 ${testStep}:`, test.content);
    ws.send(JSON.stringify(test));
}
EOF

# 检查是否安装了Node.js和ws模块
if command -v node >/dev/null 2>&1; then
    echo "运行WebSocket测试..."
    if npm list ws >/dev/null 2>&1 || npm list -g ws >/dev/null 2>&1; then
        timeout 30 node test_websocket_context.js
    else
        echo "⚠️  需要安装ws模块: npm install ws"
        echo "跳过WebSocket测试"
    fi
else
    echo "⚠️  需要安装Node.js才能运行WebSocket测试"
    echo "跳过WebSocket测试"
fi

# 清理临时文件
rm -f test_websocket_context.js

echo ""
echo "=== 测试完成 ==="
echo ""
echo "📋 测试总结："
echo "1. HTTP API 多轮对话上下文测试"
echo "2. 多语言对话上下文测试"
echo "3. WebSocket 多轮对话上下文测试"
echo ""
echo "🔍 验证要点："
echo "- AI是否能记住用户的姓名"
echo "- AI是否能记住用户的职业/爱好"
echo "- conversation_id是否正确传递"
echo "- 多语言环境下上下文是否正常"
echo "- WebSocket和HTTP API上下文是否一致"
