# 多阶段构建Dockerfile for airAi

# 构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的包
RUN apk add --no-cache git ca-certificates tzdata

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags '-extldflags "-static"' -o airAi .

# 运行阶段
FROM alpine:latest

# 安装ca-certificates和tzdata
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非root用户
RUN addgroup -g 1001 -S airAi && \
    adduser -u 1001 -S airAi -G airAi

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/airAi .

# 复制配置文件和其他必要文件
COPY --from=builder /app/config.yaml .
COPY --from=builder /app/utils/locales ./utils/locales

# 创建必要的目录
RUN mkdir -p uploads logs

# 更改文件所有者
RUN chown -R airAi:airAi /app

# 切换到非root用户
USER airAi

# 暴露端口
EXPOSE 8888

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8888/health || exit 1

# 启动应用
CMD ["./airAi"]
