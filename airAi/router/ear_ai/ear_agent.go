package ear_ai

import (
	"airAi/middleware"

	"github.com/gin-gonic/gin"
)

type EarRouter struct{}

func (e *EarRouter) InitEarRouter(Router *gin.RouterGroup) {
	earRouter := Router.Group("/v1").Use(middleware.OperationRecord())
	{
		earRouter.POST("/login", airportApi.Login)                   // 登陆
		earRouter.POST("/forgotPassword", airportApi.ForgotPassword) // 忘记密码
		earRouter.POST("/googleLogin", airportApi.GoogleLogin)       // google登陆
		earRouter.POST("/register", airportApi.Register)             // 注册
		earRouter.POST("/bindPhone", airportApi.BindPhone)           // 绑定手机号
		earRouter.POST("/getCode", airportApi.SmCode)                // 获取验证码

	}
}

func (e *EarRouter) InitEarAuthorRouter(Router *gin.RouterGroup) {
	//earRouter := Router.Group("v1").Use(middleware.JWTAuth())
	earRouter := Router.Group("v1")
	{
		earRouter.POST("/recognize", airportApi.Recognize)   // 上传文件
		earRouter.POST("/updateUser", airportApi.UpdateUser) // 修改用户
		earRouter.GET("/chat", airportApi.Chat)              // ws聊天
		earRouter.POST("/tts", airportApi.TextToSpeech)      // 聊天
		earRouter.POST("/syncChat", airportApi.SyncChat)     // 同步聊天

	}
}
