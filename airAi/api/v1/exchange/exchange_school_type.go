package exchange

import (
	"exchange_server/model/common/response"
	"exchange_server/model/exchange"
	req "exchange_server/model/exchange/request"
	"github.com/gin-gonic/gin"
)

func (s *AdminExchangeSchoolApi) CreateSchoolType(c *gin.Context) {
	var info exchange.SchoolType
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := schoolService.AddSchoolType(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(info, c)
}

func (s *AdminExchangeSchoolApi) GetSchoolTypeList(c *gin.Context) {
	var info req.SchoolTypePageReq
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := schoolService.GetSchoolTypeList(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{List: list,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize}, "success", c)
}

func (s *AdminExchangeSchoolApi) GetSchoolTypeAllList(c *gin.Context) {
	var info req.SchoolTypePageReq
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := schoolService.GetSchoolTypeAllList(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{List: list,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize}, "success", c)
}

func (s *AdminExchangeSchoolApi) SetSchoolType(c *gin.Context) {
	var info exchange.SchoolType
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := schoolService.SetSchoolType(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

func (s *AdminExchangeSchoolApi) DeleteSchoolType(c *gin.Context) {
	var info exchange.SchoolType
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := schoolService.DeleteSchoolType(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}
