package exchange

import (
	"exchange_server/global"
	"exchange_server/model/common/response"
	"exchange_server/model/exchange"
	req "exchange_server/model/exchange/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AdminUserApi struct {
}

func (s *AdminUserApi) UserList(c *gin.Context) {
	var info req.UserPageReq
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage("invalid params", c)
		return
	}
	list, total, err := userService.UserList(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{List: list,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize}, "success", c)
}

func (s *AdminUserApi) SetUserInfo(c *gin.Context) {
	var info exchange.User
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage("invalid params", c)
		return
	}
	err := userService.UpdateUser(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

func (s *AdminUserApi) KycList(c *gin.Context) {
	var info req.KycPageReq
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage("invalid params", c)
		return
	}
	list, total, err := userService.GetKycList(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{List: list,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize}, "success", c)
}

func (s *AdminUserApi) UpdateKyc(c *gin.Context) {
	var kyc exchange.Kyc
	if err := c.ShouldBind(&kyc); err != nil {
		global.GVA_LOG.Error("update kyc  failed", zap.Error(err))
		response.FailWithMessage("invalid params", c)
		return
	}
	err := userService.UpdateKyc(kyc)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

func (s *AdminUserApi) AuthenticationList(c *gin.Context) {
	var info req.AuthenticationPageReq
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage("invalid params", c)
		return
	}
	list, total, err := userService.AuthenticationList(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{List: list,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize}, "success", c)
}

func (s *AdminUserApi) UpdateAuthentication(c *gin.Context) {
	var info exchange.PersonalAuthentication
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage("invalid params", c)
		return
	}
	_, err := userService.UpdatePersonalAuthentication(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}
