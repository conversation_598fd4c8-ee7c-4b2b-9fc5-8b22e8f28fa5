package exchange

import (
	"exchange_server/model/common/response"
	"exchange_server/model/exchange"
	req "exchange_server/model/exchange/request"
	"exchange_server/utils"
	"github.com/gin-gonic/gin"
)

type AdminTokenApi struct {
}

func (s *AdminTokenApi) CreateToken(c *gin.Context) {
	var info exchange.TokenPrice
	if err := c.ShouldBindJSON(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := tokenService.AddToken(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)

}
func (s *AdminTokenApi) DeleteToken(c *gin.Context) {
	var info exchange.TokenPrice
	if err := c.ShouldBindJSON(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := tokenService.DeleteToken(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

func (s *AdminTokenApi) TokenList(c *gin.Context) {
	var info req.TokenPageReq
	if err := c.ShouldBindJSON(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if info.Lang == "" {
		info.Lang = utils.GetLang(c)
	}
	list, total, err := tokenService.GetTokenPriceListByType(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{List: list,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize}, "success", c)
}

func (s *AdminTokenApi) UpdateToken(c *gin.Context) {
	var info exchange.TokenPrice
	if err := c.ShouldBindJSON(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := tokenService.UpdateTokenPrice(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

func (s *AdminTokenApi) DeleteTokenName(c *gin.Context) {
	var info exchange.TokenName
	if err := c.ShouldBindJSON(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := tokenService.DeleteTokenName(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}
