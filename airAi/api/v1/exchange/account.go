package exchange

import (
	"exchange_server/model/common/response"
	"exchange_server/model/exchange"
	req "exchange_server/model/exchange/request"
	"exchange_server/utils"
	"github.com/gin-gonic/gin"
)

func (s *AdminUserApi) SetWithdrawal(c *gin.Context) {
	var info exchange.Withdrawal
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage("invalid params", c)
		return
	}
	err := userService.SetWithdrawal(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

func (s *AdminUserApi) GetWithdrawal(c *gin.Context) {
	var l req.WithdrawalPageReq
	if err := c.ShouldBind<PERSON>uery(&l); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := utils.Verify(l, utils.PageInfoVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := userService.GetWithdrawalList(l)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{List: list,
		Total:    total,
		Page:     l.Page,
		PageSize: l.PageSize}, "success", c)
}
