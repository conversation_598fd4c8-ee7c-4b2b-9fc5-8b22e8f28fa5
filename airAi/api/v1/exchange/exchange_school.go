package exchange

import (
	"exchange_server/model/common/response"
	"exchange_server/model/exchange"
	req "exchange_server/model/exchange/request"
	"github.com/gin-gonic/gin"
)

type AdminExchangeSchoolApi struct {
}

func (s *AdminExchangeSchoolApi) CreateSchool(c *gin.Context) {
	var info exchange.School
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := schoolService.AddSchool(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(info, c)
}

func (s *AdminExchangeSchoolApi) GetSchoolList(c *gin.Context) {
	var info req.SchoolPageReq
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := schoolService.GetSchoolList(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{List: list,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize}, "success", c)
}

func (s *AdminExchangeSchoolApi) GetSchoolInfo(c *gin.Context) {
	var info exchange.School
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	schoolInfo, err := schoolService.GetSchoolInfo(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(schoolInfo, c)
}

func (s *AdminExchangeSchoolApi) SetSchool(c *gin.Context) {
	var info exchange.School
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := schoolService.SetSchool(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

func (s *AdminExchangeSchoolApi) DeleteSchool(c *gin.Context) {
	var info exchange.School
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := schoolService.DeleteSchool(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}
