package exchange

import "exchange_server/service"

type ApiGroup struct {
	AdminTokenApi
	AdminUserApi
	AdminBannerApi
	AdminDashboardApi
	AdminExchangeSchoolApi
	AdminExchangeActivitiesApi
	AdminLevelApi
}

var (
	tokenService      = service.ServiceGroupApp.ExcServiceGroup.TokenService
	userService       = service.ServiceGroupApp.ExcServiceGroup.UserService
	newsService       = service.ServiceGroupApp.ExcServiceGroup.NewsService
	bannerService     = service.ServiceGroupApp.ExcServiceGroup.BannerService
	dashboardService  = service.ServiceGroupApp.ExcServiceGroup.DashboardService
	schoolService     = service.ServiceGroupApp.ExcServiceGroup.SchoolService
	activitiesService = service.ServiceGroupApp.ExcServiceGroup.ActivitiesService
	levelService      = service.ServiceGroupApp.ExcServiceGroup.LevelService
)
