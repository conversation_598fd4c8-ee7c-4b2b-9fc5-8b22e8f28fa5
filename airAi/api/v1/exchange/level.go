package exchange

import (
	"exchange_server/model/common/response"
	"exchange_server/model/exchange"
	req "exchange_server/model/exchange/request"
	"exchange_server/utils"
	"github.com/gin-gonic/gin"
)

type AdminLevelApi struct {
}

func (s *AdminLevelApi) List(c *gin.Context) {
	var l req.LevelReq
	err := c.ShouldBindQuery(&l)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(l, utils.PageInfoVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := levelService.List(l)
	response.OkWithDetailed(response.PageResult{List: list,
		Total:    total,
		Page:     l.Page,
		PageSize: l.PageSize}, "success", c)

}

func (s *AdminLevelApi) Update(c *gin.Context) {
	var l exchange.LevelRequest
	err := c.ShouldBindJSON(&l)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = levelService.Update(l)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	}
	response.Ok(c)
}
