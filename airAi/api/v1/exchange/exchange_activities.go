package exchange

import (
	"exchange_server/model/common/response"
	"exchange_server/model/exchange"
	req "exchange_server/model/exchange/request"
	"exchange_server/utils"
	"github.com/gin-gonic/gin"
)

type AdminExchangeActivitiesApi struct {
}

func (s *AdminExchangeActivitiesApi) CreateActivities(c *gin.Context) {
	var info exchange.Activities
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := activitiesService.CreateActivities(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(info, c)
}

func (s *AdminExchangeActivitiesApi) GetActivitiesList(c *gin.Context) {
	var l req.ActivitiesReq
	err := c.ShouldBindQuery(&l)
	if err != nil {
		response.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	err = utils.Verify(l, utils.PageInfoVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := activitiesService.GetActivities(l)
	response.OkWithDetailed(response.PageResult{List: list,
		Total:    total,
		Page:     l.Page,
		PageSize: l.PageSize}, "success", c)

}

func (s *AdminExchangeActivitiesApi) GetActivitiesInfo(c *gin.Context) {
	var info exchange.Activities
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	schoolInfo, err := activitiesService.GetActivitiesInfo(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(schoolInfo, c)
}

func (s *AdminExchangeActivitiesApi) SetActivities(c *gin.Context) {
	var info exchange.Activities
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := activitiesService.SetActivities(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

func (s *AdminExchangeActivitiesApi) DeleteActivities(c *gin.Context) {
	var info exchange.Activities
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := activitiesService.DeleteActivities(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}
