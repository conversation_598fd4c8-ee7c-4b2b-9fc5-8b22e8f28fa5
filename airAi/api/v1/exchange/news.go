package exchange

import (
	"exchange_server/model/common/response"
	"exchange_server/model/exchange"
	"exchange_server/model/exchange/request"
	"github.com/gin-gonic/gin"
)

type AdminNewsApi struct {
}

func (s *AdminNewsApi) GetNewsList(c *gin.Context) {
	var info request.NewsPageReq
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage("invalid params", c)
		return
	}
	list, total, err := newsService.GetNewsList(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{List: list,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize}, "success", c)
}

func (s *AdminNewsApi) GetNews(c *gin.Context) {
	var info exchange.NewsArticle
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage("invalid params", c)
		return
	}
	newsInfo, err := newsService.GetNewsInfoById(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(newsInfo, "success", c)
}

func (s *AdminNewsApi) UpdateNews(c *gin.Context) {
	var info exchange.NewsArticle
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage("invalid params", c)
		return
	}
	err := newsService.UpdateNews(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("success", c)
}

func (s *AdminNewsApi) DeleteNews(c *gin.Context) {
	var info exchange.NewsArticle
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage("invalid params", c)
		return
	}
	err := newsService.DeleteNews(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("success", c)
}
