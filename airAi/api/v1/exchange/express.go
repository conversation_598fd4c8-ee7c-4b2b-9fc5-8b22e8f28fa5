package exchange

import (
	"exchange_server/model/common/response"
	"exchange_server/model/exchange"
	"exchange_server/model/exchange/request"
	"exchange_server/utils"
	"github.com/gin-gonic/gin"
)

func (s *AdminNewsApi) DeleteExpress(c *gin.Context) {
	var info exchange.Express
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage("invalid params", c)
		return
	}
	err := newsService.DeleteExpress(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("success", c)
}

func (s *AdminNewsApi) GetExpressList(c *gin.Context) {
	var info request.NewsPageReq
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage("invalid params", c)
		return
	}
	if info.Lang == "" {
		info.Lang = utils.GetLang(c)
	}
	list, total, err := newsService.GetExpressList(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{List: list,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize}, "success", c)
}
