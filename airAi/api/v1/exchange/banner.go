package exchange

import (
	"exchange_server/model/common/response"
	"exchange_server/model/exchange"
	req "exchange_server/model/exchange/request"
	"github.com/gin-gonic/gin"
)

type AdminBannerApi struct {
}

func (s *AdminBannerApi) CreateBanner(c *gin.Context) {
	var info exchange.Banner
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := bannerService.AddBanner(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(info, c)
}

func (s *AdminBannerApi) GetBannerList(c *gin.Context) {
	var info req.BannerPageReq
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := bannerService.GetBannerList(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{List: list,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize}, "success", c)
}

func (s *AdminBannerApi) SetBanner(c *gin.Context) {
	var info exchange.Banner
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := bannerService.SetBanner(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

func (s *AdminBannerApi) DeleteBanner(c *gin.Context) {
	var info exchange.Banner
	if err := c.ShouldBind(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := bannerService.DeleteBanner(info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}
