package ear

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"airAi/common/requst"
	airRsq "airAi/common/response"
	"airAi/core/i18n"
	"airAi/global"
	"airAi/middleware"
	"airAi/service"
	"airAi/utils"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

// 升级HTTP连接到WebSocket
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许所有跨域请求
	},
}

// WebSocket连接用户ID映射
var wsUserMap = make(map[*websocket.Conn]uint)

// getUserIDFromWebSocket 从WebSocket连接获取用户ID
func getUserIDFromWebSocket(conn *websocket.Conn) uint {
	if userID, exists := wsUserMap[conn]; exists {
		return userID
	}
	return 0
}

// setUserIDForWebSocket 为WebSocket连接设置用户ID
func setUserIDForWebSocket(conn *websocket.Conn, userID uint) {
	wsUserMap[conn] = userID
}

// removeUserIDForWebSocket 移除WebSocket连接的用户ID映射
func removeUserIDForWebSocket(conn *websocket.Conn) {
	delete(wsUserMap, conn)
}

// WebSocketMessage WebSocket消息结构
type WebSocketMessage struct {
	Type           string `json:"type"`            // "chat" 或 "ping"
	Content        string `json:"content"`         // 消息内容
	Stream         bool   `json:"stream"`          // 是否使用流式响应
	Language       string `json:"language"`        // 语言代码，如 "zh_CN", "en", "id", "hi"
	ConversationID string `json:"conversation_id"` // 对话ID，用于多轮对话
}

// Validate 验证WebSocket消息的有效性
func (msg *WebSocketMessage) ValidateWithLang(lang string) error {
	if msg.Type == "" {
		return fmt.Errorf(i18n.TSimple(lang, "websocket.message_type_empty"))
	}

	if msg.Type == "chat" && msg.Content == "" {
		return fmt.Errorf(i18n.TSimple(lang, "websocket.chat_content_empty"))
	}

	if msg.Type != "chat" && msg.Type != "ping" {
		return fmt.Errorf(i18n.T(lang, "websocket.message_type_unsupported", map[string]interface{}{
			"Type": msg.Type,
		}))
	}

	return nil
}

// Validate 验证WebSocket消息的有效性（保持向后兼容）
func (msg *WebSocketMessage) Validate() error {
	return msg.ValidateWithLang("zh_CN")
}

// WebSocketResponse WebSocket响应结构
type WebSocketResponse struct {
	Type           string `json:"type"`                      // "partial", "complete", "error", "pong"
	Content        string `json:"content"`                   // 响应内容
	Sequence       int    `json:"sequence,omitempty"`        // 序列号（流式响应时使用）
	IsFinal        bool   `json:"is_final,omitempty"`        // 是否为最后一个块
	Error          string `json:"error,omitempty"`           // 错误信息
	ConversationID string `json:"conversation_id,omitempty"` // 对话ID
}

// Chat
// @Tags     Ear
// @Summary  聊天推送 websocket
// @Produce   application/json
// @Success  200   {object}  response.Response{data=string,msg=string}  "返回消息返回数据"
// @Router   /v1/chat [get]
func (b *AirportApi) Chat(c *gin.Context) {
	// 获取用户ID进行配额检查
	userID := utils.GetUserID(c)
	if userID == 0 {
		global.GVA_LOG.Warn("WebSocket连接缺少用户认证信息")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未认证用户"})
		return
	}

	// 升级HTTP连接到WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		global.GVA_LOG.Error("WebSocket upgrade failed:", zap.Error(err))
		return
	}
	defer func() {
		global.WsClient.Delete(conn)
		removeUserIDForWebSocket(conn) // 清理用户ID映射
		conn.Close()
	}()
	global.WsClient.Store(conn, true)

	// 设置WebSocket连接的用户ID
	setUserIDForWebSocket(conn, userID)
	// WebSocket通信循环
	for {
		// 读取客户端发送的消息
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			handleWebSocketError(conn, err, "读取消息")
			break
		}

		var writeErr error

		// 记录原始消息内容用于调试
		messageStr := string(message)
		global.GVA_LOG.Debug("收到WebSocket原始消息",
			zap.String("raw_message", messageStr),
			zap.Int("message_length", len(message)),
			zap.String("remote_addr", conn.RemoteAddr().String()))

		// 尝试解析JSON消息
		var wsMsg WebSocketMessage
		if err := json.Unmarshal(message, &wsMsg); err == nil {
			// 使用i18n模块的语言检测功能
			wsMsg.Language = i18n.GetLanguageFromMessage(wsMsg.Language)

			// JSON解析成功，验证消息结构
			if validationErr := wsMsg.ValidateWithLang(wsMsg.Language); validationErr != nil {
				global.GVA_LOG.Warn("WebSocket消息验证失败",
					zap.Error(validationErr),
					zap.String("type", wsMsg.Type),
					zap.String("content", wsMsg.Content),
					zap.Bool("stream", wsMsg.Stream),
					zap.String("language", wsMsg.Language),
					zap.String("remote_addr", conn.RemoteAddr().String()))

				// 发送错误响应
				errorResponse := WebSocketResponse{
					Type:    "error",
					Content: "",
					Error: i18n.T(wsMsg.Language, "websocket.message_validation_failed", map[string]interface{}{
						"Error": validationErr.Error(),
					}),
				}
				writeErr = sendWebSocketResponse(conn, errorResponse)
			} else {
				// 结构化消息验证通过
				global.GVA_LOG.Debug("成功解析并验证结构化WebSocket消息",
					zap.String("type", wsMsg.Type),
					zap.String("content", wsMsg.Content),
					zap.Bool("stream", wsMsg.Stream),
					zap.String("language", wsMsg.Language),
					zap.String("remote_addr", conn.RemoteAddr().String()))

				switch wsMsg.Type {
				case "ping":
					writeErr = handlePingWithLang(conn, messageType, wsMsg.Language)
				case "chat":
					if wsMsg.Stream {
						global.GVA_LOG.Info(i18n.TSimple(wsMsg.Language, "websocket.stream_chat_request"),
							zap.String("content", wsMsg.Content),
							zap.String("language", wsMsg.Language),
							zap.String("conversation_id", wsMsg.ConversationID),
							zap.String("remote_addr", conn.RemoteAddr().String()))
						writeErr = handleStreamChatWithContext(conn, messageType, wsMsg.Content, wsMsg.Language, wsMsg.ConversationID)
					} else {
						global.GVA_LOG.Info(i18n.TSimple(wsMsg.Language, "websocket.sync_chat_request"),
							zap.String("content", wsMsg.Content),
							zap.String("language", wsMsg.Language),
							zap.String("conversation_id", wsMsg.ConversationID),
							zap.String("remote_addr", conn.RemoteAddr().String()))
						writeErr = handleSyncChatWithContext(conn, messageType, wsMsg.Content, wsMsg.Language, wsMsg.ConversationID)
					}
				default:
					global.GVA_LOG.Warn(i18n.TSimple(wsMsg.Language, "websocket.unknown_message_type"),
						zap.String("type", wsMsg.Type),
						zap.String("content", wsMsg.Content),
						zap.String("language", wsMsg.Language),
						zap.String("conversation_id", wsMsg.ConversationID))
					writeErr = handleSyncChatWithContext(conn, messageType, wsMsg.Content, wsMsg.Language, wsMsg.ConversationID)
				}
			}
		} else {
			// JSON解析失败，记录详细错误信息
			global.GVA_LOG.Warn("JSON解析失败，使用纯文本消息处理",
				zap.Error(err),
				zap.String("raw_message", messageStr),
				zap.String("remote_addr", conn.RemoteAddr().String()))

			if messageStr == "ping" {
				writeErr = handlePing(conn, messageType)
			} else {
				writeErr = handleSyncChat(conn, messageType, messageStr)
			}
		}

		if writeErr != nil {
			handleWebSocketError(conn, writeErr, "发送响应")
			break
		}
	}
}

func handlePing(conn *websocket.Conn, messageType int) error {
	return handlePingWithLang(conn, messageType, "zh_CN")
}

func handlePingWithLang(conn *websocket.Conn, messageType int, lang string) error {
	response := WebSocketResponse{
		Type:    "pong",
		Content: i18n.TSimple(lang, "websocket.pong"),
	}
	return sendWebSocketResponse(conn, response)
}

// handleSyncChat 处理同步聊天（兼容旧版本）
func handleSyncChat(conn *websocket.Conn, messageType int, userMessage string) error {
	return handleSyncChatWithLang(conn, messageType, userMessage, "zh_CN")
}

// handleSyncChatWithLang 处理同步聊天（支持多语言）
func handleSyncChatWithLang(conn *websocket.Conn, messageType int, userMessage string, lang string) error {
	return handleSyncChatWithContext(conn, messageType, userMessage, lang, "")
}

// handleSyncChatWithContext 处理同步聊天（支持对话上下文）
func handleSyncChatWithContext(conn *websocket.Conn, messageType int, userMessage string, lang string, conversationID string) error {
	// 验证消息内容
	if len(userMessage) == 0 {
		global.GVA_LOG.Warn(i18n.TSimple(lang, "websocket.message_empty"))
		response := WebSocketResponse{
			Type:    "error",
			Content: "",
			Error:   i18n.TSimple(lang, "websocket.message_empty"),
		}
		return sendWebSocketResponse(conn, response)
	}

	// 从连接中获取用户ID（需要在连接建立时设置）
	userID := getUserIDFromWebSocket(conn)
	if userID == 0 {
		global.GVA_LOG.Warn("WebSocket连接缺少用户ID")
		response := WebSocketResponse{
			Type:    "error",
			Content: "",
			Error:   i18n.TSimple(lang, "websocket.auth_required"),
		}
		return sendWebSocketResponse(conn, response)
	}

	// 检查配额
	quotaService := service.ServiceGroupApp.AirportServiceGroup.QuotaService
	canUse, quota, err := quotaService.CheckQuota(userID)
	if err != nil {
		global.GVA_LOG.Error("WebSocket配额检查失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		response := WebSocketResponse{
			Type:    "error",
			Content: "",
			Error:   i18n.T(lang, "quota.check_failed", map[string]interface{}{"Error": err.Error()}),
		}
		return sendWebSocketResponse(conn, response)
	}

	if !canUse {
		global.GVA_LOG.Warn("WebSocket用户配额不足",
			zap.Uint("user_id", userID),
			zap.Int("remaining", quota.GetRemainingQuota()))
		response := WebSocketResponse{
			Type:    "error",
			Content: "",
			Error:   i18n.TSimple(lang, "quota.insufficient"),
		}
		return sendWebSocketResponse(conn, response)
	}

	global.GVA_LOG.Debug("WebSocket收到同步聊天消息",
		zap.String("user_message", userMessage),
		zap.String("language", lang),
		zap.String("conversation_id", conversationID),
		zap.String("remote_addr", conn.RemoteAddr().String()))

	// 获取对话管理器并构建上下文
	conversationManager := service.GetConversationManager()

	// 创建临时请求结构来构建上下文
	tempRequest := &requst.ChatRequest{
		Text:           userMessage,
		ConversationID: conversationID,
		Language:       lang,
	}

	messages, finalConversationID := conversationManager.BuildContextFromRequest(tempRequest)

	// 使用Eino服务生成带上下文的响应
	einoService := service.GetEinoService()
	responseContent, err := einoService.GenerateResponseWithContext(context.Background(), messages)
	if err != nil {
		global.GVA_LOG.Error("WebSocket同步聊天生成响应失败",
			zap.Error(err),
			zap.String("user_message", userMessage),
			zap.String("conversation_id", finalConversationID),
			zap.String("language", lang),
			zap.String("remote_addr", conn.RemoteAddr().String()))

		response := WebSocketResponse{
			Type:    "error",
			Content: "",
			Error: i18n.T(lang, "websocket.response_generation_failed", map[string]interface{}{
				"Error": err.Error(),
			}),
		}
		return sendWebSocketResponse(conn, response)
	}

	// 将用户消息和AI响应添加到对话上下文
	conversationManager.AddMessage(finalConversationID, "user", userMessage)
	conversationManager.AddMessage(finalConversationID, "assistant", responseContent)

	// 扣减配额
	requestInfo := map[string]interface{}{
		"request_path":    "/v1/chat",
		"request_method":  "WebSocket",
		"client_ip":       conn.RemoteAddr().String(),
		"user_agent":      "",
		"conversation_id": finalConversationID,
		"request_size":    len(userMessage),
		"response_size":   len(responseContent),
	}

	if err := quotaService.UseQuota(userID, 1, requestInfo); err != nil { // 1 = UsageTypeChat
		global.GVA_LOG.Error("WebSocket配额扣减失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		// 配额扣减失败不影响响应，但要记录错误
	}

	global.GVA_LOG.Debug("WebSocket同步聊天响应成功",
		zap.String("user_message", userMessage),
		zap.String("conversation_id", finalConversationID),
		zap.String("language", lang),
		zap.Int("response_length", len(responseContent)))

	// 发送完整响应给客户端
	response := WebSocketResponse{
		Type:           "complete",
		Content:        responseContent,
		IsFinal:        true,
		ConversationID: finalConversationID,
	}
	return sendWebSocketResponse(conn, response)
}

// handleStreamChat 处理流式聊天（兼容旧版本）
func handleStreamChat(conn *websocket.Conn, messageType int, userMessage string) error {
	return handleStreamChatWithLang(conn, messageType, userMessage, "zh_CN")
}

// handleStreamChatWithLang 处理流式聊天（支持多语言）
func handleStreamChatWithLang(conn *websocket.Conn, messageType int, userMessage string, lang string) error {
	return handleStreamChatWithContext(conn, messageType, userMessage, lang, "")
}

// handleStreamChatWithContext 处理流式聊天（支持对话上下文）
func handleStreamChatWithContext(conn *websocket.Conn, messageType int, userMessage string, lang string, conversationID string) error {
	// 验证消息内容
	if len(userMessage) == 0 {
		global.GVA_LOG.Warn(i18n.TSimple(lang, "websocket.message_empty"))
		response := WebSocketResponse{
			Type:    "error",
			Content: "",
			Error:   i18n.TSimple(lang, "websocket.message_empty"),
		}
		return sendWebSocketResponse(conn, response)
	}

	// 从连接中获取用户ID并检查配额
	userID := getUserIDFromWebSocket(conn)
	if userID == 0 {
		global.GVA_LOG.Warn("WebSocket连接缺少用户ID")
		response := WebSocketResponse{
			Type:    "error",
			Content: "",
			Error:   i18n.TSimple(lang, "websocket.auth_required"),
		}
		return sendWebSocketResponse(conn, response)
	}

	// 检查配额
	quotaService := service.ServiceGroupApp.AirportServiceGroup.QuotaService
	canUse, quota, err := quotaService.CheckQuota(userID)
	if err != nil {
		global.GVA_LOG.Error("WebSocket配额检查失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		response := WebSocketResponse{
			Type:    "error",
			Content: "",
			Error:   i18n.T(lang, "quota.check_failed", map[string]interface{}{"Error": err.Error()}),
		}
		return sendWebSocketResponse(conn, response)
	}

	if !canUse {
		global.GVA_LOG.Warn("WebSocket用户配额不足",
			zap.Uint("user_id", userID),
			zap.Int("remaining", quota.GetRemainingQuota()))
		response := WebSocketResponse{
			Type:    "error",
			Content: "",
			Error:   i18n.TSimple(lang, "quota.insufficient"),
		}
		return sendWebSocketResponse(conn, response)
	}

	global.GVA_LOG.Debug("WebSocket收到流式聊天消息",
		zap.String("user_message", userMessage),
		zap.String("language", lang),
		zap.String("conversation_id", conversationID),
		zap.String("remote_addr", conn.RemoteAddr().String()))

	// 获取对话管理器并构建上下文
	conversationManager := service.GetConversationManager()

	// 创建临时请求结构来构建上下文
	tempRequest := &requst.ChatRequest{
		Text:           userMessage,
		ConversationID: conversationID,
		Language:       lang,
	}

	messages, finalConversationID := conversationManager.BuildContextFromRequest(tempRequest)

	// 使用Eino服务生成带上下文的流式响应
	einoService := service.GetEinoService()
	streamChan, err := einoService.StreamResponseWithContext(context.Background(), messages)
	if err != nil {
		global.GVA_LOG.Error("WebSocket创建流式响应失败",
			zap.Error(err),
			zap.String("user_message", userMessage),
			zap.String("conversation_id", finalConversationID),
			zap.String("language", lang),
			zap.String("remote_addr", conn.RemoteAddr().String()))

		response := WebSocketResponse{
			Type:    "error",
			Content: "",
			Error: i18n.T(lang, "websocket.stream_response_failed", map[string]interface{}{
				"Error": err.Error(),
			}),
		}
		return sendWebSocketResponse(conn, response)
	}

	// 处理流式数据
	var fullResponse string
	for chunk := range streamChan {
		// 累积完整响应内容
		if chunk.Type == "partial" {
			fullResponse += chunk.Content
		} else if chunk.Type == "complete" {
			fullResponse = chunk.Content
		}

		response := WebSocketResponse{
			Type:           chunk.Type,
			Content:        chunk.Content,
			Sequence:       chunk.Sequence,
			IsFinal:        chunk.IsFinal,
			ConversationID: finalConversationID,
		}

		if chunk.Error != "" {
			response.Error = chunk.Error
		}

		err := sendWebSocketResponse(conn, response)
		if err != nil {
			global.GVA_LOG.Error("WebSocket发送流式响应失败",
				zap.Error(err),
				zap.String("user_message", userMessage),
				zap.String("conversation_id", finalConversationID),
				zap.String("language", lang),
				zap.Int("sequence", chunk.Sequence))
			return err
		}

		// 如果是最终块且不是错误，保存对话上下文
		if chunk.IsFinal && chunk.Type != "error" {
			conversationManager.AddMessage(finalConversationID, "user", userMessage)
			conversationManager.AddMessage(finalConversationID, "assistant", fullResponse)
		}

		// 如果是最终块或错误，结束流
		if chunk.IsFinal || chunk.Type == "error" {
			break
		}
	}

	global.GVA_LOG.Debug("WebSocket流式聊天响应完成",
		zap.String("user_message", userMessage),
		zap.String("conversation_id", finalConversationID),
		zap.String("language", lang),
		zap.String("remote_addr", conn.RemoteAddr().String()))

	return nil
}

// sendWebSocketResponse 发送WebSocket响应
func sendWebSocketResponse(conn *websocket.Conn, response WebSocketResponse) error {
	jsonData, err := json.Marshal(response)
	if err != nil {
		global.GVA_LOG.Error("序列化WebSocket响应失败", zap.Error(err))
		return err
	}
	return conn.WriteMessage(websocket.TextMessage, jsonData)
}

// isWebSocketConnClosed 检查WebSocket连接是否已关闭
func isWebSocketConnClosed(err error) bool {
	if err == nil {
		return false
	}

	// 检查常见的连接关闭错误
	errStr := err.Error()
	return strings.Contains(errStr, "connection reset by peer") ||
		strings.Contains(errStr, "broken pipe") ||
		strings.Contains(errStr, "use of closed network connection") ||
		websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway, websocket.CloseAbnormalClosure)
}

// handleWebSocketError 处理WebSocket错误
func handleWebSocketError(conn *websocket.Conn, err error, context string) {
	if isWebSocketConnClosed(err) {
		global.GVA_LOG.Debug("WebSocket连接已关闭",
			zap.String("context", context),
			zap.String("remote_addr", conn.RemoteAddr().String()))
	} else {
		global.GVA_LOG.Error("WebSocket错误",
			zap.Error(err),
			zap.String("context", context),
			zap.String("remote_addr", conn.RemoteAddr().String()))
	}
}

// SyncChat 统一聊天接口（支持同步和流式响应）
// @Tags     Ear
// @Summary  统一聊天接口
// @Description 根据请求中的stream字段决定返回方式：stream=true时返回SSE流式响应，stream=false或未设置时返回标准JSON响应
// @accept    application/json
// @Produce   application/json,text/event-stream
// @Param     data  body    requst.ChatRequest           true  "聊天请求，支持stream字段控制响应方式"
// @Success  200   {object}  response.Response{data=map[string]interface{},msg=string}  "同步响应：返回消息数据"
// @Success  200   {string}  string  "流式响应：Server-Sent Events数据流"
// @Router   /v1/syncChat [post]
func (b *AirportApi) SyncChat(c *gin.Context) {
	// 获取语言设置（优先从Context缓存获取）
	lang := i18n.GetLangFromContext(c)

	var l requst.ChatRequest
	if err := c.ShouldBindJSON(&l); err != nil {
		global.GVA_LOG.Warn(i18n.TSimple(lang, "http.parameter_binding_failed"), zap.Error(err))
		airRsq.FailWithMessage(i18n.TSimple(lang, "http.parameter_binding_failed"), c)
		return
	}

	err := utils.Verify(l, utils.ChatVerify)
	if err != nil {
		global.GVA_LOG.Warn(i18n.TSimple(lang, "http.parameter_validation_failed"),
			zap.Error(err),
			zap.String("user_text", l.Text),
			zap.String("language", lang))
		airRsq.FailWithMessage(i18n.TSimple(lang, "http.parameter_validation_failed"), c)
		return
	}

	// 根据stream字段决定响应方式
	if l.Stream {
		// 流式响应
		b.handleStreamResponse(c, &l, lang)
	} else {
		// 同步响应
		b.handleSyncResponse(c, &l, lang)
	}
}

// sendSSEEvent 发送SSE事件
func (b *AirportApi) sendSSEEvent(c *gin.Context, eventType string, data interface{}) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		global.GVA_LOG.Error("序列化SSE数据失败", zap.Error(err))
		return
	}

	// 发送事件类型
	if eventType != "" {
		fmt.Fprintf(c.Writer, "event: %s\n", eventType)
	}

	// 发送数据，处理多行数据
	dataStr := string(jsonData)
	for _, line := range strings.Split(dataStr, "\n") {
		fmt.Fprintf(c.Writer, "data: %s\n", line)
	}

	// 发送空行结束事件
	fmt.Fprintf(c.Writer, "\n")
}

// TextToSpeech 文字转语音接口
// @Tags     Ear
// @Summary  文字转语音
// @Description 将输入文本转换为MP3格式的语音音频文件，支持多种语音参数配置
// @accept    application/json
// @Produce   application/json
// @Param     data  body    requst.ChatRequest           true  "文字转语音请求，包含要转换的文本内容"
// @Param     Accept-Language  header  string  false  "语言设置，影响语音合成语言"  default(zh_CN)
// @Success  200   {object}  response.Response{data=airRsq.TtsResponse,msg=string}  "返回MP3格式音频数据的字节数组"
// @Failure  400   {object}  response.Response  "请求参数错误或文本为空"
// @Failure  401   {object}  response.Response  "认证失败"
// @Failure  413   {object}  response.Response  "文本长度超出限制"
// @Failure  500   {object}  response.Response  "语音合成服务错误"
// @Router   /v1/tts [post]
func (b *AirportApi) TextToSpeech(c *gin.Context) {
	// 获取语言设置
	lang := i18n.GetLangFromContext(c)

	var l requst.ChatRequest
	if err := c.ShouldBindJSON(&l); err != nil {
		global.GVA_LOG.Warn(i18n.TSimple(lang, "http.parameter_binding_failed"), zap.Error(err))
		airRsq.FailWithMessage(i18n.TSimple(lang, "http.parameter_binding_failed"), c)
		return
	}

	err := utils.Verify(l, utils.ChatVerify)
	if err != nil {
		global.GVA_LOG.Warn(i18n.TSimple(lang, "http.parameter_validation_failed"),
			zap.Error(err),
			zap.String("user_text", l.Text),
			zap.String("language", lang))
		airRsq.FailWithMessage(i18n.TSimple(lang, "http.parameter_validation_failed"), c)
		return
	}

	global.GVA_LOG.Debug("收到文字转语音请求",
		zap.String("user_text", l.Text),
		zap.String("language", lang),
		zap.String("client_ip", c.ClientIP()))

	audioData, err := airportService.TextToSpeech(l.Text)
	if err != nil {
		global.GVA_LOG.Error("文字转语音失败",
			zap.Error(err),
			zap.String("user_text", l.Text),
			zap.String("language", lang),
			zap.String("client_ip", c.ClientIP()))

		errorMessage := i18n.T(lang, "tts.generation_failed", map[string]interface{}{
			"Error": err.Error(),
		})
		airRsq.FailWithMessage(errorMessage, c)
		return
	}

	// 保存音频文件
	outputFile := filepath.Join(".", "output.mp3")
	if err := os.WriteFile(outputFile, audioData, 0644); err != nil {
		global.GVA_LOG.Error("保存音频文件失败",
			zap.Error(err),
			zap.String("output_file", outputFile),
			zap.String("language", lang))

		errorMessage := i18n.T(lang, "tts.save_file_failed", map[string]interface{}{
			"Error": err.Error(),
		})
		airRsq.FailWithMessage(errorMessage, c)
		return
	}

	global.GVA_LOG.Debug("文字转语音完成",
		zap.String("user_text", l.Text),
		zap.String("output_file", outputFile),
		zap.Int("audio_size", len(audioData)),
		zap.String("language", lang))

	// 构建响应数据
	data := map[string]interface{}{
		"output": audioData,
	}

	// 添加配额信息到响应中
	middleware.AddQuotaInfoToResponse(c, data)

	// 设置配额信息到响应头
	middleware.GetQuotaInfoHeader(c)

	airRsq.OkWithData(data, c)
}

// handleSyncResponse 处理同步响应
func (b *AirportApi) handleSyncResponse(c *gin.Context, req *requst.ChatRequest, lang string) {
	global.GVA_LOG.Debug("收到同步聊天请求",
		zap.String("user_text", req.Text),
		zap.String("language", lang),
		zap.String("client_ip", c.ClientIP()))

	// 获取对话管理器
	conversationManager := service.GetConversationManager()

	// 构建对话上下文
	messages, conversationID := conversationManager.BuildContextFromRequest(req)

	// 使用Eino服务生成响应
	einoService := service.GetEinoService()
	response, err := einoService.GenerateResponseWithContext(c.Request.Context(), messages)
	if err != nil {
		global.GVA_LOG.Error("同步聊天生成响应失败",
			zap.Error(err),
			zap.String("user_text", req.Text),
			zap.String("conversation_id", conversationID),
			zap.String("language", lang),
			zap.String("client_ip", c.ClientIP()))

		errorMessage := i18n.T(lang, "websocket.response_generation_failed", map[string]interface{}{
			"Error": err.Error(),
		})
		airRsq.FailWithMessage(errorMessage, c)
		return
	}

	// 将用户消息和AI响应添加到对话上下文
	conversationManager.AddMessage(conversationID, "user", req.Text)
	conversationManager.AddMessage(conversationID, "assistant", response)

	global.GVA_LOG.Debug(i18n.TSimple(lang, "http.sync_chat_completed"),
		zap.String("user_text", req.Text),
		zap.String("conversation_id", conversationID),
		zap.String("language", lang),
		zap.Int("response_length", len(response)))

	// 构建响应数据
	data := make(map[string]interface{})
	data["info"] = response
	data["conversation_id"] = conversationID

	// 添加配额信息到响应中
	middleware.AddQuotaInfoToResponse(c, data)

	// 设置配额信息到响应头
	middleware.GetQuotaInfoHeader(c)

	airRsq.OkWithData(data, c)
}

// handleStreamResponse 处理流式响应
func (b *AirportApi) handleStreamResponse(c *gin.Context, req *requst.ChatRequest, lang string) {
	global.GVA_LOG.Debug("收到流式聊天请求",
		zap.String("user_text", req.Text),
		zap.String("language", lang),
		zap.String("client_ip", c.ClientIP()))

	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 获取对话管理器
	conversationManager := service.GetConversationManager()

	// 构建对话上下文
	messages, conversationID := conversationManager.BuildContextFromRequest(req)

	// 获取Eino服务并开始流式响应
	einoService := service.GetEinoService()
	streamChan, err := einoService.StreamResponseWithContext(c.Request.Context(), messages)
	if err != nil {
		global.GVA_LOG.Error("创建流式响应失败",
			zap.Error(err),
			zap.String("user_text", req.Text),
			zap.String("conversation_id", conversationID),
			zap.String("language", lang),
			zap.String("client_ip", c.ClientIP()))

		// 发送错误事件
		errorMessage := i18n.T(lang, "http.stream_response_failed", map[string]interface{}{
			"Error": err.Error(),
		})
		errorData := map[string]interface{}{
			"type":    "error",
			"content": errorMessage,
			"error":   err.Error(),
		}
		b.sendSSEEvent(c, "error", errorData)
		return
	}

	// 处理流式响应
	flusher, ok := c.Writer.(http.Flusher)
	if !ok {
		global.GVA_LOG.Error("响应写入器不支持流式输出")
		airRsq.FailWithMessage(i18n.TSimple(lang, "http.server_not_support_stream"), c)
		return
	}

	// 发送开始事件
	startData := map[string]interface{}{
		"type":            "start",
		"content":         "",
		"message":         i18n.TSimple(lang, "http.stream_chat_start"),
		"conversation_id": conversationID,
	}
	b.sendSSEEvent(c, "start", startData)
	flusher.Flush()

	// 处理流式数据，添加心跳机制防止长时间连接超时
	var fullResponse string
	keepAliveTicker := time.NewTicker(15 * time.Second) // 每15秒发送一次心跳
	defer keepAliveTicker.Stop()

	for {
		select {
		case <-c.Request.Context().Done():
			global.GVA_LOG.Debug(i18n.TSimple(lang, "http.client_disconnected"),
				zap.String("user_text", req.Text),
				zap.String("conversation_id", conversationID),
				zap.String("language", lang),
				zap.String("client_ip", c.ClientIP()))
			return
		case <-keepAliveTicker.C:
			// 发送心跳事件保持连接活跃
			heartbeatData := map[string]interface{}{
				"type":    "heartbeat",
				"content": "",
				"message": "connection_alive",
			}
			b.sendSSEEvent(c, "heartbeat", heartbeatData)
			flusher.Flush()
			global.GVA_LOG.Debug("发送SSE心跳保持连接",
				zap.String("conversation_id", conversationID),
				zap.String("client_ip", c.ClientIP()))
		case chunk, ok := <-streamChan:
			if !ok {
				// 流式通道已关闭，结束处理
				return
			}

			// 累积完整响应内容
			if chunk.Type == "partial" {
				fullResponse += chunk.Content
			} else if chunk.Type == "complete" {
				fullResponse = chunk.Content
			}

			// 发送数据块
			eventType := "data"
			if chunk.Type == "error" {
				eventType = "error"
			} else if chunk.IsFinal {
				eventType = "complete"
			}

			// 为完成事件添加conversation_id
			chunkData := chunk
			if chunk.IsFinal && chunk.Type != "error" {
				// 将用户消息和AI响应添加到对话上下文
				conversationManager.AddMessage(conversationID, "user", req.Text)
				conversationManager.AddMessage(conversationID, "assistant", fullResponse)
			}

			b.sendSSEEvent(c, eventType, chunkData)
			flusher.Flush()

			// 如果是最终块或错误，结束流
			if chunk.IsFinal || chunk.Type == "error" {
				return
			}
		}
	}

	global.GVA_LOG.Debug(i18n.TSimple(lang, "http.stream_chat_completed"),
		zap.String("user_text", req.Text),
		zap.String("conversation_id", conversationID),
		zap.String("language", lang),
		zap.String("client_ip", c.ClientIP()))
}
