package ear

import (
	"airAi/common/requst"
	res "airAi/common/response"
	"airAi/global"
	"airAi/utils"
	"model/airpods"
	"model/system"
	"model/system/request"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Login
// @Tags      Ear
// @Summary   用户登录
// @Description 支持手机号/邮箱+密码登录或验证码登录两种方式
// @accept    application/json
// @Produce   application/json
// @Param     data  body    requst.LoginRequest           true  "登录请求，包含账号、密码或验证码"
// @Param     Accept-Language  header  string  false  "语言设置，支持zh_CN、en、id、hi"  default(zh_CN)
// @Success   200   {object}  response.Response{data=res.LoginResponse,msg=string}  "登录成功，返回用户信息和token"
// @Failure   400   {object}  response.Response  "请求参数错误"
// @Failure   401   {object}  response.Response  "账号或密码错误"
// @Failure   403   {object}  response.Response  "账号已被冻结"
// @Failure   500   {object}  response.Response  "服务器内部错误"
// @Router    /v1/login [post]
func (b *AirportApi) Login(c *gin.Context) {
	var l requst.LoginRequest
	if err := c.ShouldBindJSON(&l); err != nil {
		res.FailWithMessage(err.Error(), c)
		return
	}
	err := utils.Verify(l, utils.LoginVerify)
	if err != nil {
		res.FailWithMessage(err.Error(), c)
		return
	}
	user, err := airportService.Login(l)
	if err != nil {
		global.GVA_LOG.Error("user login error ", zap.Error(err))
		res.FailWithMessage("invalid user", c)
		return
	}
	token, claims, err := utils.LoginToken(user)
	if err != nil {
		global.GVA_LOG.Error("get token fail!", zap.Error(err))
		res.FailWithMessage("get token fail", c)
		return
	}
	utils.SetToken(c, token, int(claims.RegisteredClaims.ExpiresAt.Unix()-time.Now().Unix()))
	res.OkWithDetailed(res.LoginResponse{
		User: res.User{
			UUID:      user.UUID,
			Username:  user.Username,
			NickName:  user.NickName,
			HeaderImg: user.HeaderImg,
			Phone:     user.Phone,
			Email:     user.Email,
			Enable:    user.Enable,
		},
		Token:     token,
		ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
	}, "success", c)
}

// ForgotPassword
// @Tags      Ear
// @Summary   忘记密码
// @Description 通过手机号或邮箱重置用户密码，需要验证码验证
// @accept    application/json
// @Produce   application/json
// @Param     data  body    requst.LoginRequest           true  "忘记密码请求，包含账号和验证码"
// @Success   200   {object}  response.Response{data=res.LoginResponse,msg=string}  "密码重置成功，返回用户信息和token"
// @Failure   400   {object}  response.Response  "请求参数错误"
// @Failure   401   {object}  response.Response  "验证码错误或已过期"
// @Failure   404   {object}  response.Response  "用户不存在"
// @Failure   500   {object}  response.Response  "服务器内部错误"
// @Router    /v1/forgotPassword [post]
func (b *AirportApi) ForgotPassword(c *gin.Context) {
	var l requst.LoginRequest
	if err := c.ShouldBindJSON(&l); err != nil {
		res.FailWithMessage(err.Error(), c)
		return
	}
	err := utils.Verify(l, utils.LoginVerify)
	if err != nil {
		res.FailWithMessage(err.Error(), c)
		return
	}
	user, err := airportService.ForgotPassword(l)
	if err != nil {
		global.GVA_LOG.Error("user login error ", zap.Error(err))
		res.FailWithMessage("invalid user", c)
		return
	}
	token, claims, err := utils.LoginToken(user)
	if err != nil {
		global.GVA_LOG.Error("get token fail!", zap.Error(err))
		res.FailWithMessage("get token fail", c)
		return
	}
	utils.SetToken(c, token, int(claims.RegisteredClaims.ExpiresAt.Unix()-time.Now().Unix()))
	res.OkWithDetailed(res.LoginResponse{
		User: res.User{
			UUID:      user.UUID,
			Username:  user.Username,
			NickName:  user.NickName,
			HeaderImg: user.HeaderImg,
			Phone:     user.Phone,
			Email:     user.Email,
			Enable:    user.Enable,
		},
		Token:     token,
		ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
	}, "success", c)
}

// LogOut
// @Tags      Ear
// @Summary   退出登陆
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=string,msg=string}  "上传文件示例,返回包括文件详情"
// @Router    /v1/logOut [get]
func (b *AirportApi) LogOut(c *gin.Context) {
	token := utils.GetToken(c)
	var jwtList system.JwtBlacklist
	err := global.GVA_DB.Create(&jwtList).Error
	if err != nil {
		res.FailWithMessage(err.Error(), c)
		return
	}
	global.BlackCache.SetDefault(token, struct{}{})
	res.Ok(c)
}

// BindPhone
// @Tags      Ear
// @Summary   绑定手机号
// @Security  ApiKeyAuth
// @accept    multipart/form-data
// @Produce   application/json
// @Param    data  body     requst.BindPhone           true  "phone"                                                         true  "上传文件示例"
// @Success   200   {object}  response.Response{data=string,msg=string}  "绑定手机号"
// @Router    /v1/bindPhone [post]
func (b *AirportApi) BindPhone(c *gin.Context) {
	var l requst.BindPhone
	if err := c.ShouldBind(&l); err != nil {
		res.FailWithMessage(err.Error(), c)
		return
	}
	err := utils.Verify(l, utils.BindPhoneVerify)
	if err != nil {
		res.FailWithMessage(err.Error(), c)
		return
	}
	uid := utils.GetUserID(c)
	err = airportService.BindPhone(int64(uid), l.Phone)
	if err != nil {
		res.FailWithMessage(err.Error(), c)
		return
	}
	res.OkWithMessage("bind phone success", c)
}

// Register
// @Tags     Ear
// @Summary  用户注册
// @Description 通过手机号或邮箱注册新用户账号，需要验证码验证
// @accept    application/json
// @Produce   application/json
// @Param    data  body    request.Register                                           true  "注册请求，包含手机号/邮箱、密码、确认密码、验证码"
// @Param     Accept-Language  header  string  false  "语言设置，支持zh_CN、en、id、hi"  default(zh_CN)
// @Success  200   {object}  response.Response{data=res.LoginResponse,msg=string}  "注册成功，返回用户信息和token"
// @Failure  400   {object}  response.Response  "请求参数错误或密码不匹配"
// @Failure  409   {object}  response.Response  "用户已存在"
// @Failure  422   {object}  response.Response  "验证码错误或已过期"
// @Failure  500   {object}  response.Response  "服务器内部错误"
// @Router   /v1/register [post]
func (b *AirportApi) Register(c *gin.Context) {
	var l request.Register
	if err := c.ShouldBindJSON(&l); err != nil {
		res.FailWithMessage(err.Error(), c)
		return
	}
	err := utils.Verify(l, utils.RegisterVerify)
	if err != nil {
		res.FailWithMessage(err.Error(), c)
		return
	}
	user, err := airportService.Register(l)
	if err != nil {
		res.FailWithMessage(err.Error(), c)
		return
	}
	token, claims, err := utils.LoginToken(user)
	if err != nil {
		global.GVA_LOG.Error("get token fail!", zap.Error(err))
		res.FailWithMessage("get token fail", c)
		return
	}
	utils.SetToken(c, token, int(claims.RegisteredClaims.ExpiresAt.Unix()-time.Now().Unix()))
	res.OkWithDetailed(res.LoginResponse{
		User: res.User{
			UUID:      user.UUID,
			Username:  user.Username,
			NickName:  user.NickName,
			HeaderImg: user.HeaderImg,
			Phone:     user.Phone,
			Email:     user.Email,
			Enable:    user.Enable,
		},
		Token:     token,
		ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
	}, "success", c)
}

// GoogleLogin
// @Tags      Ear
// @Summary   Google登陆
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param    data  body     airpods.AirpodsUser          true  "phone"                                                              true  "上传文件示例"
// @Success   200   {object}  response.Response{data=response.Response,msg=string}  "Google登陆"
// @Router    /v1/googleLogin [post]
func (b *AirportApi) GoogleLogin(c *gin.Context) {
	res.FailWithMessage("Temporarily not supported", c)
}

// UpdateUser
// @Tags      Ear
// @Summary   更新用户信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param    data  body     airpods.AirpodsUser          true  "phone"                                                           true  "上传文件示例"
// @Success   200   {object}  response.Response{data=string,msg=string}  "更新用户信息"
// @Router    /v1/updateUser [post]
func (b *AirportApi) UpdateUser(c *gin.Context) {
	var l airpods.AirpodsUser
	if err := c.ShouldBindJSON(&l); err != nil {
		res.FailWithMessage(err.Error(), c)
		return
	}
	err := airportService.UpdateUser(l)
	if err != nil {
		global.GVA_LOG.Error("update user fail!", zap.Error(err))
		res.FailWithMessage("update user fail", c)
		return
	}
	res.OkWithMessage("update user success", c)
}
