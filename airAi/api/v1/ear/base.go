package ear

import (
	"airAi/common/requst"
	"airAi/common/response"
	"airAi/core/consts"
	"airAi/global"
	"airAi/other_api/brevo"
	"airAi/other_api/sms"
	"airAi/utils"
	"context"
	"crypto/ecdsa"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"net/http"
	"os"
	"time"
)

// SmCode
// @Tags     Exchange
// @Summary  发送验证码
// @Produce   application/json
// @Param    data  body      requst.RegisterCode                                            true  "手机号, 密码, 验证码"
// @Success  200   {object}  response.Response  "返回包括用户信息,token,过期时间"
// @Router   /v1/getCode [post]
func (b *AirportApi) SmCode(c *gin.Context) {
	var info requst.RegisterCode
	if err := c.ShouldBindJSON(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	code := utils.GenerateOTP()
	expirationTime := consts.SmsExpirationMinTime * time.Minute
	if utils.PhoneValidation(info.Account) {
		if !utils.IsDomesticPhoneNumberWithIntl(info.Account) {
			// 发送短信验证码
			_, err := sms.SendMessage(info.Account, int64(code))
			if err != nil {
				response.FailWithMessage(err.Error(), c)
				return
			}
		}
	}
	if utils.EmailValidation(info.Account) {
		_, err := brevo.SendEmailMessage(info.Account, int64(code))
		if err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}

	}
	err := global.GVA_REDIS.Set(context.Background(), info.Account, code, expirationTime).Err()
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	info.Code = int64(code)
	response.OkWithDetailed(info, "successful", c)
}

var (
	googleOauthConfig *oauth2.Config
	oauthStateString  = "random" // 生产环境应该使用更安全的随机字符串
)

func (b *AirportApi) GoogleCallback(c *gin.Context) {
	// 初始化Google OAuth配置
	googleOauthConfig = &oauth2.Config{
		RedirectURL:  os.Getenv("REDIRECT_URL"),
		ClientID:     os.Getenv("carbon-beanbag-455607-n1"),
		ClientSecret: os.Getenv("AlzaSyC9Xt2CCDGoTl8Cc6x1t9m_n-nnE63DYUY"),
		Scopes: []string{
			"https://www.googleapis.com/auth/userinfo.email",
			"https://www.googleapis.com/auth/userinfo.profile",
		},
		Endpoint: google.Endpoint,
	}
	// 验证state参数
	state := c.Query("state")
	if state != oauthStateString {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid oauth state"})
		return
	}

	// 获取授权码
	code := c.Query("code")
	token, err := googleOauthConfig.Exchange(context.Background(), code)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to exchange token: " + err.Error()})
		return
	}
	// 获取用户信息
	userInfo, err := getUserInfo(token.AccessToken)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to get user info: " + err.Error()})
		return
	}
	fmt.Println(userInfo)
}

type UserInfo struct {
	ID      string `json:"id"`
	Email   string `json:"email"`
	Name    string `json:"name"`
	Picture string `json:"picture"`
}

// 获取用户信息
func getUserInfo(accessToken string) (*UserInfo, error) {
	resp, err := http.Get("https://www.googleapis.com/oauth2/v2/userinfo?access_token=" + accessToken)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var userInfo UserInfo
	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, err
	}

	return &userInfo, nil
}

////// apple login

type AppleConfig struct {
	TeamID      string
	ClientID    string
	KeyID       string
	RedirectURL string
	PrivateKey  string
}

type AppleTokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	IDToken      string `json:"id_token"`
}

type AppleUser struct {
	Email string `json:"email"`
	Sub   string `json:"sub"` // 用户的唯一标识
}

var appleConfig AppleConfig

func (b *AirportApi) AppleCallback(c *gin.Context) {
	appleConfig = AppleConfig{
		TeamID:      os.Getenv("APPLE_TEAM_ID"),
		ClientID:    os.Getenv("APPLE_CLIENT_ID"),
		KeyID:       os.Getenv("APPLE_KEY_ID"),
		RedirectURL: os.Getenv("APPLE_REDIRECT_URL"),
		PrivateKey:  os.Getenv("APPLE_PRIVATE_KEY"),
	}
	// 验证state (生产环境应该从session/cookie中验证)
	state := c.PostForm("state")
	if state == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid state"})
		return
	}

	// 获取授权码
	code := c.PostForm("code")
	if code == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "authorization code not found"})
		return
	}

	// 生成客户端密钥
	clientSecret, err := generateAppleClientSecret()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to generate client secret"})
		return
	}
	// 交换令牌
	token, err := exchangeToken(code, clientSecret)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to exchange token: " + err.Error()})
		return
	}

	// 解析ID Token获取用户信息
	user, err := parseAppleIDToken(token.IDToken)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse id token: " + err.Error()})
		return
	}
	fmt.Println(user)
}

// 生成Apple客户端密钥
func generateAppleClientSecret() (string, error) {
	// 解析PEM格式的私钥
	block, _ := pem.Decode([]byte(appleConfig.PrivateKey))
	if block == nil {
		return "", errors.New("failed to parse PEM block containing the private key")
	}

	// 解析ECDSA私钥
	privKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return "", fmt.Errorf("failed to parse private key: %v", err)
	}

	ecdsaPrivateKey, ok := privKey.(*ecdsa.PrivateKey)
	if !ok {
		return "", errors.New("private key is not of type ECDSA")
	}
	// 创建JWT claims
	now := time.Now()
	claims := jwt.RegisteredClaims{
		Issuer:    appleConfig.TeamID,
		IssuedAt:  jwt.NewNumericDate(now),
		ExpiresAt: jwt.NewNumericDate(now.Add(time.Hour * 24 * 180)), // 6个月有效期
		Audience:  jwt.ClaimStrings{"https://appleid.apple.com"},
		Subject:   appleConfig.ClientID,
	}

	// 创建JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodES256, claims)
	token.Header["kid"] = appleConfig.KeyID

	// 签名并生成客户端密钥
	return token.SignedString(ecdsaPrivateKey)
}

// 使用授权码交换访问令牌
func exchangeToken(code, clientSecret string) (*AppleTokenResponse, error) {
	oauthConfig := &oauth2.Config{
		ClientID:     appleConfig.ClientID,
		ClientSecret: clientSecret,
		RedirectURL:  appleConfig.RedirectURL,
		Endpoint: oauth2.Endpoint{
			TokenURL: "https://appleid.apple.com/auth/token",
		},
	}

	token, err := oauthConfig.Exchange(context.Background(), code)
	if err != nil {
		return nil, err
	}

	// 将oauth2.Token转换为我们的结构体
	tokenBytes, _ := json.Marshal(token)
	var appleToken AppleTokenResponse
	json.Unmarshal(tokenBytes, &appleToken)

	return &appleToken, nil
}

// 解析Apple的ID Token
func parseAppleIDToken(idToken string) (*AppleUser, error) {
	// 注意: 实际生产环境应该验证Apple的签名
	token, _, err := new(jwt.Parser).ParseUnverified(idToken, jwt.MapClaims{})
	if err != nil {
		return nil, err
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("invalid claims")
	}

	// 验证必要字段
	if _, ok := claims["sub"]; !ok {
		return nil, errors.New("sub claim is required")
	}

	user := &AppleUser{
		Sub: claims["sub"].(string),
	}

	// 邮箱可能在claims中 (第一次登录时)
	if email, ok := claims["email"]; ok {
		user.Email = email.(string)
	}

	return user, nil
}
