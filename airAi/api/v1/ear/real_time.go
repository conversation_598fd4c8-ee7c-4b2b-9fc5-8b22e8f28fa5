package ear

import (
	airRsq "airAi/common/response"

	"github.com/gin-gonic/gin"
)

// Recognize
// @Tags     Ear
// @Summary  语音识别转文字
// @Description 上传音频文件进行语音识别，支持多种音频格式，返回识别的文字内容
// @accept    multipart/form-data
// @Produce   application/json
// @Param     file  formData  file true  "音频文件，支持wav、mp3、m4a等格式，文件大小限制10MB"
// @Param     Accept-Language  header  string  false  "语言设置，影响识别准确度"  default(zh_CN)
// @Success  200   {object}  response.Response{data=airRsq.RealTime,msg=string}  "识别成功，返回文字内容"
// @Failure  400   {object}  response.Response  "文件格式不支持或文件损坏"
// @Failure  413   {object}  response.Response  "文件大小超出限制"
// @Failure  422   {object}  response.Response  "音频内容无法识别"
// @Failure  500   {object}  response.Response  "语音识别服务错误"
// @Router   /v1/recognize [post]
func (b *AirportApi) Recognize(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		airRsq.FailWithMessage("Failed to upload", c)
		return
	}
	// 打开文件
	src, err := file.Open()
	if err != nil {
		airRsq.FailWithMessage("Failed to open", c)
		return
	}
	defer src.Close()
	str, err := airportService.RealTime(src)
	if err != nil {
		airRsq.FailWithMessage(err.Error(), c)
		return
	}

	// 构建响应数据
	data := map[string]interface{}{
		"text": str,
	}

	// 添加配额信息到响应中（如果有的话）
	if quotaInfo, exists := c.Get("quota_info"); exists {
		data["quota_info"] = quotaInfo
	}

	airRsq.OkWithData(data, c)
}
