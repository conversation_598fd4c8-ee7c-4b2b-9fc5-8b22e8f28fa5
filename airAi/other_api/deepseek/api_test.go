package deepseek

import (
	"fmt"
	"testing"
)

func TestChat(t *testing.T) {
	client := NewClient("sk-b2eba490d2b449aa893657aa3fe33ce9")
	// 构建聊天请求
	chatReq := ChatRequest{
		Model: "deepseek-chat",
		Messages: []Message{
			{
				Role:    "user",
				Content: "hello world",
			},
		},
		MaxTokens: 2048,
	}

	rsp, err := client.Chat(chatReq)
	if err != nil {
		t.Error(err)
	}
	if rsp.Error != nil {
		t.Error(rsp.Error)
	}
	t.Log(rsp)
	fmt.Printf("%+v\n", rsp)
}
