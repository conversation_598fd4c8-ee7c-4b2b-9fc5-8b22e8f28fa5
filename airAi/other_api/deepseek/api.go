package deepseek

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

// Client 封装 DeepSeek API 客户端
type Client struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
}

// NewClient 创建新的 DeepSeek 客户端
func NewClient(apiKey string) *Client {
	return &Client{
		apiKey:     apiKey,
		baseURL:    "https://api.deepseek.com/v1",
		httpClient: &http.Client{},
	}
}

// ChatMessage 发送聊天请求
func (c *Client) ChatMessage(msg string) (*ChatResponse, error) {
	// 构建聊天请求
	chatReq := ChatRequest{
		Model: "deepseek-chat",
		Messages: []Message{
			{
				Role:    "user",
				Content: msg,
			},
		},
		MaxTokens: 2048,
	}
	return c.Chat(chatReq)
}

// Chat 发送聊天请求
func (c *Client) Chat(req ChatRequest) (*ChatResponse, error) {
	endpoint := c.baseURL + "/chat/completions"
	return c.doRequest(endpoint, req)
}

// doRequest 执行API请求
func (c *Client) doRequest(endpoint string, payload interface{}) (*ChatResponse, error) {
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	httpReq, err := http.NewRequest("POST", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}
	var apiResp ChatResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	return &apiResp, nil
}
