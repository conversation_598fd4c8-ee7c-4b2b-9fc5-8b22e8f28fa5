package xunfei

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gorilla/websocket"
	"io"
	"log"
	"net/http"
	"os"
	"sync"
	"time"
)

// WebSocketClient 封装了一个自动重连的 WebSocket 客户端
type WebSocketClient struct {
	URL          string
	Conn         *websocket.Conn
	retryDelay   time.Duration
	pingInterval time.Duration
	doneChan     chan struct{}
}

// 客户端结构体
type Client struct {
	config       Config
	Conn         *websocket.Conn
	dialer       *websocket.Dialer
	pingInterval time.Duration
	sessionLock  sync.Mutex
	sessions     map[string]*session // sid -> session
	doneChan     chan struct{}
}

type session struct {
	conn       *websocket.Conn
	userID     string
	resultChan chan Result
	closeChan  chan struct{}
	wg         sync.WaitGroup
}

// 新建客户端
func NewClient(cfg Config) *Client {
	if cfg.FrameSize <= 0 {
		cfg.FrameSize = defaultFrameSize
	}

	return &Client{
		config:       cfg,
		dialer:       &websocket.Dialer{HandshakeTimeout: 5 * time.Second},
		pingInterval: 30 * time.Second,
		sessions:     make(map[string]*session),
		doneChan:     make(chan struct{}),
	}
}

const (
	statusFirst      = 0
	statusContinue   = 1
	statusLast       = 2
	defaultFrameSize = 1280
)

// 配置结构体
type Config struct {
	Host      string
	AppID     string
	APIKey    string
	APISecret string
	FrameSize int // 可选，默认1280
}

// 识别结果
type Result struct {
	Text   string
	Uid    string
	Status int
	IsEnd  bool
	Sid    string
}

// 识别音频文件 (同步阻塞)
func (c *Client) RecognizeFile(ctx context.Context, filePath, userID string) ([]Result, error) {
	audioFile, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("open file failed: %w", err)
	}
	defer audioFile.Close()

	return c.Recognize(ctx, audioFile, userID)
}

// 识别音频流 (同步阻塞)
//
//	func (c *Client) Recognize(ctx context.Context, audio io.Reader, userID string) ([]Result, error) {
//		sess := &session{
//			conn:       c.Conn,
//			userID:     userID,
//			resultChan: make(chan Result, 10),
//			closeChan:  make(chan struct{}),
//		}
//
//		sess.wg.Add(2)
//		// 注册会话
//		c.sessionLock.Lock()
//		c.sessions[userID] = sess
//		c.sessionLock.Unlock()
//
//		// 启动接收消息的 goroutine
//		go func() {
//			defer sess.wg.Done()
//			c.ReceiveResponse(sess)
//		}()
//
//		// 启动发送音频的 goroutine
//		go func() {
//			defer sess.wg.Done()
//			if err := c.SendAudio(ctx, sess, audio); err != nil {
//				log.Printf("SendAudio error: %v", err)
//				close(sess.resultChan) // 关闭 resultChan 防止死锁
//			}
//		}()
//
//		// 收集结果
//		var results []Result
//		for result := range sess.resultChan {
//			results = append(results, result)
//			if result.IsEnd {
//				break
//			}
//		}
//
//		// 等待所有 goroutine 结束
//		sess.wg.Wait()
//		return results, nil
//	}
//
// 识别音频流 (同步阻塞)
func (c *Client) Recognize(ctx context.Context, audio io.Reader, userID string) ([]Result, error) {
	sess := &session{
		conn:       c.Conn,
		userID:     userID,
		resultChan: make(chan Result, 10),
		closeChan:  make(chan struct{}),
	}

	sess.wg.Add(2)
	// 注册会话
	c.sessionLock.Lock()
	c.sessions[userID] = sess
	c.sessionLock.Unlock()

	var once sync.Once // 使用 sync.Once 确保通道只关闭一次

	// 启动接收消息的 goroutine
	go func() {
		defer sess.wg.Done()
		c.ReceiveResponse(sess)
	}()

	// 启动发送音频的 goroutine
	go func() {
		defer sess.wg.Done()
		if err := c.SendAudio(ctx, sess, audio); err != nil {
			log.Printf("SendAudio error: %v", err)
			once.Do(func() {
				close(sess.resultChan) // 确保只关闭一次 resultChan
			})
		}
	}()

	// 收集结果
	var results []Result
	for result := range sess.resultChan {
		results = append(results, result)
		if result.IsEnd {
			break
		}
	}

	// 等待所有 goroutine 结束
	sess.wg.Wait()
	return results, nil
}

// ConnectWebSocket 创建WebSocket连接
func (c *Client) ConnectWebSocket() (*websocket.Conn, error) {
	authUrl := assembleAuthUrl(c.config.Host, c.config.APIKey, c.config.APISecret)
	conn, resp, err := c.dialer.Dial(authUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("dial failed: %s, %w", readResp(resp), err)
	}
	if resp.StatusCode != http.StatusSwitchingProtocols {
		return nil, fmt.Errorf("invalid status: %d", resp.StatusCode)
	}
	c.Conn = conn
	return conn, nil
}

// 发送音频数据
func (c *Client) SendAudio(ctx context.Context, sess *session, audio io.Reader) error {
	buffer := make([]byte, c.config.FrameSize)
	status := statusFirst

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-sess.closeChan:
			return errors.New("connection closed")
		default:
		}

		n, err := audio.Read(buffer)
		if err != nil {
			if err == io.EOF {
				status = statusLast
			} else {
				return fmt.Errorf("read audio failed: %w", err)
			}
		}

		frame := c.buildAudioFrame(buffer[:n], status)
		if err := c.Conn.WriteJSON(frame); err != nil {
			return fmt.Errorf("send frame failed: %w", err)
		}

		if status == statusFirst {
			status = statusContinue
		} else if status == statusLast {
			return nil
		}

		time.Sleep(40 * time.Millisecond) // 模拟音频间隔
	}
}

// 接收识别结果
func (c *Client) ReceiveResponse(sess *session) {
	defer close(sess.resultChan)

	for {
		_, message, err := sess.conn.ReadMessage()
		if err != nil {
			log.Printf("read message error: %v", err)
			return
		}

		var result XfResponse
		if err := json.Unmarshal(message, &result); err != nil {
			log.Printf("Failed to unmarshal JSON: %v", err)
			continue
		}

		var text string
		for _, ws := range result.Data.Result.Ws {
			for _, cw := range ws.Cw {
				text += cw.W
			}
		}

		sess.resultChan <- Result{
			Text:   text,
			Status: result.Data.Result.Status,
			Uid:    sess.userID,
			IsEnd:  result.Data.Result.Status == 2,
			Sid:    result.Sid,
		}
		fmt.Println("result.Data.Result.Status == ", result.Data.Result.Status)
		return
		//if result.Data.Result.Status == 2 {
		//	return
		//}
	}
}

// 构建音频帧
func (c *Client) buildAudioFrame(data []byte, status int) interface{} {
	if status == statusFirst {
		return map[string]interface{}{
			"common": map[string]interface{}{
				"app_id": c.config.AppID,
			},
			"business": map[string]interface{}{
				"language": "zh_cn",
				"domain":   "iat",
				"accent":   "mandarin",
			},
			"data": map[string]interface{}{
				"status":   status,
				"format":   "audio/L16;rate=16000",
				"audio":    base64.StdEncoding.EncodeToString(data),
				"encoding": "raw",
			},
		}
	}

	return map[string]interface{}{
		"data": map[string]interface{}{
			"status":   status,
			"format":   "audio/L16;rate=16000",
			"audio":    base64.StdEncoding.EncodeToString(data),
			"encoding": "raw",
		},
	}
}
