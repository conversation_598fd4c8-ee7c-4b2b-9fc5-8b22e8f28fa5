package main

import (
	"airAi/core"
	"airAi/core/i18n"
	"airAi/global"
	"airAi/initialize"
	"airAi/service"
	"context"

	"go.uber.org/zap"
)

//go:generate go env -w GO111MODULE=on
//go:generate go env -w GOPROXY=https://goproxy.cn,direct
//go:generate go mod tidy
//go:generate go mod download
func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)

	// 初始化国际化
	if err := i18n.Initialize(); err != nil {
		global.GVA_LOG.Error("初始化国际化失败", zap.Error(err))
	} else {
		global.GVA_LOG.Info("国际化初始化成功")
	}
	global.GVA_DB = initialize.Gorm()
	initialize.Redis()
	if global.GVA_DB != nil {
		initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}

	// 初始化Eino服务
	ctx := context.Background()
	if err := service.InitializeEinoService(ctx); err != nil {
		global.GVA_LOG.Error("初始化Eino服务失败", zap.Error(err))
		// 注意：这里不退出程序，允许应用继续运行，但会记录错误
	} else {
		global.GVA_LOG.Info("Eino服务初始化成功")
	}

	// 初始化配额定时任务服务
	quotaScheduler := service.ServiceGroupApp.AirportServiceGroup.QuotaSchedulerService
	quotaScheduler.Start()
	global.GVA_LOG.Info("配额定时任务服务启动成功")

	core.RunWindowsServer()
}
