{"websocket": {"message_empty": {"other": "Message content cannot be empty"}, "message_type_empty": {"other": "Message type cannot be empty"}, "message_type_unsupported": {"other": "Unsupported message type: {{.Type}}"}, "chat_content_empty": {"other": "Chat message content cannot be empty"}, "json_parse_failed": {"other": "JSON parsing failed, using plain text message processing"}, "message_validation_failed": {"other": "Message format error: {{.Error}}"}, "stream_chat_request": {"other": "Processing streaming chat request"}, "sync_chat_request": {"other": "Processing synchronous chat request"}, "unknown_message_type": {"other": "Unknown message type, using synchronous chat processing"}, "stream_response_failed": {"other": "Failed to create streaming response: {{.Error}}"}, "response_generation_failed": {"other": "Failed to generate response: {{.Error}}"}, "connection_closed": {"other": "WebSocket connection closed"}, "connection_error": {"other": "WebSocket error"}, "pong": {"other": "pong"}}, "http": {"stream_chat_start": {"other": "Starting response generation"}, "stream_response_failed": {"other": "Failed to create streaming response: {{.Error}}"}, "server_not_support_stream": {"other": "Server does not support streaming output"}, "client_disconnected": {"other": "Client disconnected"}, "stream_chat_completed": {"other": "Streaming chat response completed"}, "sync_chat_completed": {"other": "Synchronous chat response completed"}, "parameter_binding_failed": {"other": "Request parameter binding failed"}, "parameter_validation_failed": {"other": "Request parameter validation failed"}}, "tts": {"generation_failed": {"other": "Text-to-speech generation failed: {{.Error}}"}, "save_file_failed": {"other": "Failed to save audio file: {{.Error}}"}}, "eino": {"chatmodel_not_initialized": {"other": "ChatModel not initialized"}, "user_message_empty": {"other": "User message cannot be empty"}, "stream_not_enabled": {"other": "Streaming response not enabled, using normal response"}, "stream_response_timeout": {"other": "Streaming response timeout"}, "stream_response_completed": {"other": "Streaming response completed"}, "stream_response_read_error": {"other": "Streaming response read error"}, "stream_response_send_failed": {"other": "Failed to send streaming response chunk"}, "response_generation_start": {"other": "Starting chat response generation"}, "response_generation_success": {"other": "Successfully generated chat response"}, "response_generation_failed": {"other": "Failed to generate chat response"}, "stream_response_start": {"other": "Starting streaming chat response generation"}, "stream_response_create_failed": {"other": "Failed to create streaming response"}, "stream_response_send_chunk": {"other": "Sending streaming response chunk"}}, "validation": {"required": {"other": "{{.Field}} is required"}, "min_length": {"other": "{{.Field}} must be at least {{.Min}} characters"}, "max_length": {"other": "{{.Field}} must be at most {{.Max}} characters"}, "invalid_format": {"other": "{{.Field}} format is invalid"}}, "quota": {"insufficient": {"other": "Quota insufficient, please purchase additional quota or subscribe to monthly service"}, "check_failed": {"other": "Quota check failed: {{.Error}}"}, "usage_failed": {"other": "Quota usage recording failed: {{.Error}}"}, "purchase_success": {"other": "Quota purchase successful"}, "subscription_success": {"other": "Monthly subscription successful"}, "reset_success": {"other": "Monthly quota reset successful"}, "info_get_failed": {"other": "Failed to get quota information: {{.Error}}"}, "purchase_failed": {"other": "Quota purchase failed: {{.Error}}"}, "subscription_failed": {"other": "Monthly subscription failed: {{.Error}}"}, "invalid_units": {"other": "Purchase units must be greater than 0"}, "monthly_quota_info": {"other": "Monthly base quota: {{.MonthlyQuota}} requests, monthly subscription fee: {{.SubscriptionFee}} RMB"}, "extra_quota_info": {"other": "Extra quota: {{.ExtraQuotaUnit}} requests/unit, price: {{.ExtraQuotaPrice}} RMB/unit"}, "reset_failed": {"other": "Quota reset failed: {{.Error}}"}}, "common": {"success": {"other": "Operation successful"}, "failed": {"other": "Operation failed"}, "error": {"other": "Error"}, "warning": {"other": "Warning"}, "info": {"other": "Info"}}}