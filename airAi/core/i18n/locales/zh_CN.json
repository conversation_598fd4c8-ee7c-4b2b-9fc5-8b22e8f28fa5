{"websocket": {"message_empty": {"other": "消息内容不能为空"}, "message_type_empty": {"other": "消息类型不能为空"}, "message_type_unsupported": {"other": "不支持的消息类型: {{.Type}}"}, "chat_content_empty": {"other": "聊天消息内容不能为空"}, "json_parse_failed": {"other": "JSON解析失败，使用纯文本消息处理"}, "message_validation_failed": {"other": "消息格式错误: {{.Error}}"}, "stream_chat_request": {"other": "处理流式聊天请求"}, "sync_chat_request": {"other": "处理同步聊天请求"}, "unknown_message_type": {"other": "未知消息类型，使用同步聊天处理"}, "stream_response_failed": {"other": "创建流式响应失败: {{.Error}}"}, "response_generation_failed": {"other": "生成响应失败: {{.Error}}"}, "connection_closed": {"other": "WebSocket连接已关闭"}, "connection_error": {"other": "WebSocket错误"}, "pong": {"other": "pong"}}, "http": {"stream_chat_start": {"other": "开始生成响应"}, "stream_response_failed": {"other": "创建流式响应失败: {{.Error}}"}, "server_not_support_stream": {"other": "服务器不支持流式输出"}, "client_disconnected": {"other": "客户端断开连接"}, "stream_chat_completed": {"other": "流式聊天响应完成"}, "sync_chat_completed": {"other": "同步聊天响应完成"}, "parameter_binding_failed": {"other": "请求参数绑定失败"}, "parameter_validation_failed": {"other": "请求参数验证失败"}}, "tts": {"generation_failed": {"other": "文字转语音失败: {{.Error}}"}, "save_file_failed": {"other": "保存音频文件失败: {{.Error}}"}}, "eino": {"chatmodel_not_initialized": {"other": "ChatModel未初始化"}, "user_message_empty": {"other": "用户消息不能为空"}, "stream_not_enabled": {"other": "流式响应未启用，使用普通响应"}, "stream_response_timeout": {"other": "流式响应超时"}, "stream_response_completed": {"other": "流式响应完成"}, "stream_response_read_error": {"other": "流式响应读取错误"}, "stream_response_send_failed": {"other": "发送流式响应块失败"}, "response_generation_start": {"other": "开始生成聊天响应"}, "response_generation_success": {"other": "成功生成聊天响应"}, "response_generation_failed": {"other": "生成聊天响应失败"}, "stream_response_start": {"other": "开始生成流式聊天响应"}, "stream_response_create_failed": {"other": "创建流式响应失败"}, "stream_response_send_chunk": {"other": "发送流式响应块"}}, "validation": {"required": {"other": "{{.Field}}是必填项"}, "min_length": {"other": "{{.Field}}最少需要{{.Min}}个字符"}, "max_length": {"other": "{{.Field}}最多允许{{.Max}}个字符"}, "invalid_format": {"other": "{{.Field}}格式不正确"}}, "quota": {"insufficient": {"other": "配额不足，请购买额外配额或订阅月度服务"}, "check_failed": {"other": "配额检查失败: {{.Error}}"}, "usage_failed": {"other": "配额使用记录失败: {{.Error}}"}, "purchase_success": {"other": "配额购买成功"}, "subscription_success": {"other": "月度订阅成功"}, "reset_success": {"other": "月度配额重置成功"}, "info_get_failed": {"other": "获取配额信息失败: {{.Error}}"}, "purchase_failed": {"other": "配额购买失败: {{.Error}}"}, "subscription_failed": {"other": "月度订阅失败: {{.Error}}"}, "invalid_units": {"other": "购买单位数必须大于0"}, "monthly_quota_info": {"other": "月度基础配额: {{.MonthlyQuota}} 次，月度订阅费用: {{.SubscriptionFee}} 元"}, "extra_quota_info": {"other": "额外配额: {{.ExtraQuotaUnit}} 次/单位，价格: {{.ExtraQuotaPrice}} 元/单位"}}, "common": {"success": {"other": "操作成功"}, "failed": {"other": "操作失败"}, "error": {"other": "错误"}, "warning": {"other": "警告"}, "info": {"other": "信息"}}}