{"websocket": {"message_empty": {"other": "Konten pesan tidak boleh kosong"}, "message_type_empty": {"other": "<PERSON><PERSON> pesan tidak boleh kosong"}, "message_type_unsupported": {"other": "<PERSON><PERSON> pesan tidak didukung: {{.Type}}"}, "chat_content_empty": {"other": "Konten pesan chat tidak boleh kosong"}, "json_parse_failed": {"other": "Parsing <PERSON><PERSON><PERSON>, men<PERSON><PERSON><PERSON> pem<PERSON>san pesan teks biasa"}, "message_validation_failed": {"other": "Kesalahan format pesan: {{.Error}}"}, "stream_chat_request": {"other": "Memproses permintaan chat streaming"}, "sync_chat_request": {"other": "Memproses permintaan chat sinkron"}, "unknown_message_type": {"other": "<PERSON><PERSON> pesan tidak dikenal, menggunakan pemrosesan chat sinkron"}, "stream_response_failed": {"other": "Gagal membuat respons streaming: {{.Error}}"}, "response_generation_failed": {"other": "<PERSON><PERSON> respons: {{.<PERSON><PERSON><PERSON>}}"}, "connection_closed": {"other": "Koneksi WebSocket ditutup"}, "connection_error": {"other": "Kesalahan WebSocket"}, "pong": {"other": "pong"}}, "http": {"stream_chat_start": {"other": "<PERSON><PERSON><PERSON> pem<PERSON>n respons"}, "stream_response_failed": {"other": "Gagal membuat respons streaming: {{.Error}}"}, "server_not_support_stream": {"other": "Server tidak mendukung output streaming"}, "client_disconnected": {"other": "<PERSON><PERSON><PERSON> terputus"}, "stream_chat_completed": {"other": "Respons chat streaming selesai"}, "sync_chat_completed": {"other": "Respons chat sinkron selesai"}, "parameter_binding_failed": {"other": "Pengikatan parameter permintaan gagal"}, "parameter_validation_failed": {"other": "Validasi parameter permintaan gagal"}}, "tts": {"generation_failed": {"other": "<PERSON><PERSON><PERSON> teks-ke-suara gagal: {{.Error}}"}, "save_file_failed": {"other": "Gagal menyimpan file audio: {{.Error}}"}}, "eino": {"chatmodel_not_initialized": {"other": "ChatModel tidak diinisialisasi"}, "user_message_empty": {"other": "Pesan pengguna tidak boleh kosong"}, "stream_not_enabled": {"other": "Respons streaming tidak diaktifkan, menggunakan respons normal"}, "stream_response_timeout": {"other": "Timeout respons streaming"}, "stream_response_completed": {"other": "Respons streaming selesai"}, "stream_response_read_error": {"other": "Kesalahan membaca respons streaming"}, "stream_response_send_failed": {"other": "Gagal mengirim chunk respons streaming"}, "response_generation_start": {"other": "<PERSON><PERSON><PERSON> p<PERSON> respons chat"}, "response_generation_success": {"other": "<PERSON><PERSON><PERSON><PERSON> membuat respons chat"}, "response_generation_failed": {"other": "<PERSON><PERSON> membuat respons chat"}, "stream_response_start": {"other": "<PERSON><PERSON><PERSON> p<PERSON> respons chat streaming"}, "stream_response_create_failed": {"other": "<PERSON><PERSON> membuat respons streaming"}, "stream_response_send_chunk": {"other": "Mengirim chunk respons streaming"}}, "validation": {"required": {"other": "{{.<PERSON>}} wajib diisi"}, "min_length": {"other": "{{.<PERSON>}} harus minimal {{.<PERSON>}} karakter"}, "max_length": {"other": "{{.<PERSON>}} maksimal {{.<PERSON>}} karakter"}, "invalid_format": {"other": "Format {{.Field}} tidak valid"}}, "quota": {"insufficient": {"other": "<PERSON>ota tidak men<PERSON>, silakan beli kuota tambahan atau berlangganan layanan bulanan"}, "check_failed": {"other": "<PERSON><PERSON><PERSON><PERSON><PERSON> kuota gagal: {{.E<PERSON>r}}"}, "usage_failed": {"other": "Pencatatan penggunaan kuota gagal: {{.Error}}"}, "purchase_success": {"other": "Pembelian kuota ber<PERSON>il"}, "subscription_success": {"other": "<PERSON><PERSON><PERSON> bula<PERSON> be<PERSON>"}, "reset_success": {"other": "Reset kuota bulanan ber<PERSON>il"}, "info_get_failed": {"other": "Gagal mendapatkan informasi kuota: {{.Error}}"}, "purchase_failed": {"other": "Pembelian kuota gagal: {{.Error}}"}, "subscription_failed": {"other": "<PERSON><PERSON><PERSON> bulanan gagal: {{.E<PERSON>r}}"}, "invalid_units": {"other": "Unit pembelian harus lebih dari 0"}, "monthly_quota_info": {"other": "<PERSON>ota dasar bulanan: {{.MonthlyQuota}} per<PERSON><PERSON><PERSON>, biaya langganan bulanan: {{.SubscriptionFee}} RMB"}, "extra_quota_info": {"other": "Kuota tambahan: {{.ExtraQuotaUnit}} permintaan/unit, harga: {{.ExtraQuotaPrice}} RMB/unit"}}, "common": {"success": {"other": "Operasi berhasil"}, "failed": {"other": "<PERSON><PERSON> gagal"}, "error": {"other": "<PERSON><PERSON><PERSON>"}, "warning": {"other": "Peringatan"}, "info": {"other": "Informasi"}}}