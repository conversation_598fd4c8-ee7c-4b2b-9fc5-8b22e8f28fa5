package i18n

import (
	"embed"
	"encoding/json"
	"fmt"
	"strings"

	"airAi/core/consts"
	"airAi/global"

	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"go.uber.org/zap"
	"golang.org/x/text/language"
)

//go:embed locales/*.json
var localeFS embed.FS

var (
	bundle    *i18n.Bundle
	localizer map[string]*i18n.Localizer
)

// Manager i18n管理器
type Manager struct {
	bundle    *i18n.Bundle
	localizer map[string]*i18n.Localizer
}

// NewManager 创建新的i18n管理器
func NewManager() *Manager {
	return &Manager{
		localizer: make(map[string]*i18n.Localizer),
	}
}

// Initialize 初始化国际化
func Initialize() error {
	bundle = i18n.NewBundle(language.Chinese)
	bundle.RegisterUnmarshalFunc("json", json.Unmarshal)

	localizer = make(map[string]*i18n.Localizer)

	// 支持的语言列表
	supportedLangs := []string{
		consts.LangCodeCN,
		consts.LangCodeEN,
		consts.LangCodeID,
		consts.LangCodeHI,
	}

	// 加载语言文件
	for _, lang := range supportedLangs {
		filename := fmt.Sprintf("locales/%s.json", lang)
		data, err := localeFS.ReadFile(filename)
		if err != nil {
			global.GVA_LOG.Error("读取语言文件失败",
				zap.String("language", lang),
				zap.String("file", filename),
				zap.Error(err))
			continue
		}

		// 直接解析消息文件
		_, err = bundle.ParseMessageFileBytes(data, filename)
		if err != nil {
			global.GVA_LOG.Error("解析语言文件失败",
				zap.String("language", lang),
				zap.String("file", filename),
				zap.Error(err))
			continue
		}

		// 创建本地化器
		localizer[lang] = i18n.NewLocalizer(bundle, lang)

		global.GVA_LOG.Info("成功加载语言文件",
			zap.String("language", lang),
			zap.String("file", filename))
	}

	global.GVA_LOG.Info("国际化初始化完成",
		zap.Int("loaded_languages", len(localizer)))

	return nil
}

// GetLocalizer 获取本地化器
func GetLocalizer(lang string) *i18n.Localizer {
	if l, exists := localizer[lang]; exists {
		return l
	}
	// 默认返回中文本地化器
	return localizer[consts.DefaultLangCode]
}

// Translate 翻译函数
func Translate(lang, messageID string, templateData map[string]interface{}) string {
	l := GetLocalizer(lang)
	if l == nil {
		global.GVA_LOG.Warn("本地化器不存在",
			zap.String("language", lang),
			zap.String("message_id", messageID))
		return messageID
	}

	config := &i18n.LocalizeConfig{
		MessageID:    messageID,
		TemplateData: templateData,
	}

	message, err := l.Localize(config)
	if err != nil {
		global.GVA_LOG.Warn("翻译消息失败",
			zap.String("language", lang),
			zap.String("message_id", messageID),
			zap.Error(err))
		return messageID
	}

	return message
}

// T 翻译函数（简短别名）
func T(lang, messageID string, templateData map[string]interface{}) string {
	return Translate(lang, messageID, templateData)
}

// TSimple 简单翻译函数（简短别名）
func TSimple(lang, messageID string) string {
	return Translate(lang, messageID, nil)
}

// TWithContext 从Gin上下文获取语言并翻译（简短别名）
func TWithContext(c *gin.Context, messageID string, templateData map[string]interface{}) string {
	lang := GetLang(c)
	return Translate(lang, messageID, templateData)
}

// TSimpleWithContext 从Gin上下文获取语言并简单翻译（简短别名）
func TSimpleWithContext(c *gin.Context, messageID string) string {
	lang := GetLang(c)
	return Translate(lang, messageID, nil)
}

// TranslateWithContext 从Gin上下文获取语言并翻译
func TranslateWithContext(c *gin.Context, messageID string, templateData map[string]interface{}) string {
	lang := GetLanguage(c)
	return Translate(lang, messageID, templateData)
}

// TranslateSimple 简单翻译函数（无模板数据）
func TranslateSimple(lang, messageID string) string {
	return Translate(lang, messageID, nil)
}

// TranslateSimpleWithContext 从Gin上下文获取语言并简单翻译
func TranslateSimpleWithContext(c *gin.Context, messageID string) string {
	lang := GetLanguage(c)
	return Translate(lang, messageID, nil)
}

// GetSupportedLanguages 获取支持的语言列表
func GetSupportedLanguages() []string {
	var langs []string
	for lang := range localizer {
		langs = append(langs, lang)
	}
	return langs
}

// IsLanguageSupported 检查语言是否支持
func IsLanguageSupported(lang string) bool {
	_, exists := localizer[lang]
	return exists
}

// GetLanguage 从Gin上下文获取语言设置
func GetLanguage(c *gin.Context) string {
	// 首先检查是否已经在Context中缓存了语言设置
	if cachedLang, exists := c.Get("user_language"); exists {
		if lang, ok := cachedLang.(string); ok {
			return lang
		}
	}

	var detectedLang string

	// 1. 优先从查询参数lang获取
	if lang := c.Query("lang"); lang != "" && IsLanguageSupported(lang) {
		detectedLang = lang
	} else if lang := c.Query("language"); lang != "" && IsLanguageSupported(lang) {
		// 2. 从查询参数language获取
		detectedLang = lang
	} else if acceptLang := c.GetHeader("Accept-Language"); acceptLang != "" {
		// 3. 从请求头Accept-Language获取
		detectedLang = parseAcceptLanguage(acceptLang)
	} else {
		// 4. 默认返回中文
		detectedLang = consts.DefaultLangCode
	}

	// 将检测到的语言存储到Context中，避免重复解析
	c.Set("user_language", detectedLang)

	return detectedLang
}

// GetLang 从Gin上下文获取语言设置（简短别名）
func GetLang(c *gin.Context) string {
	return GetLanguage(c)
}

// GetLangFromContext 从Context中获取已缓存的语言设置（简短别名）
func GetLangFromContext(c *gin.Context) string {
	return GetLanguageFromContext(c)
}

// GetLanguageFromContext 从Context中获取已缓存的语言设置
func GetLanguageFromContext(c *gin.Context) string {
	if cachedLang, exists := c.Get("user_language"); exists {
		if lang, ok := cachedLang.(string); ok {
			return lang
		}
	}
	// 如果Context中没有缓存，则调用GetLanguage进行检测和缓存
	return GetLanguage(c)
}

// parseAcceptLanguage 解析Accept-Language头
func parseAcceptLanguage(acceptLang string) string {
	// 解析Accept-Language头，支持格式如: zh_CN, en, id, hi
	langs := strings.Split(acceptLang, ",")
	for _, lang := range langs {
		// 清理空格和权重信息
		lang = strings.TrimSpace(strings.Split(lang, ";")[0])
		if IsLanguageSupported(lang) {
			return lang
		}

		// 尝试匹配语言前缀，如 zh-CN -> zh_CN
		if strings.Contains(lang, "-") {
			normalizedLang := strings.Replace(lang, "-", "_", -1)
			if IsLanguageSupported(normalizedLang) {
				return normalizedLang
			}
		}

		// 尝试匹配语言前缀，如 zh -> zh_CN
		if len(lang) == 2 {
			switch lang {
			case "zh":
				return consts.LangCodeCN
			case "en":
				return consts.LangCodeEN
			case "id":
				return consts.LangCodeID
			case "hi":
				return consts.LangCodeHI
			}
		}
	}

	return consts.DefaultLangCode
}

// GetLanguageFromMessage 从WebSocket消息获取语言设置
func GetLanguageFromMessage(msgLang string) string {
	// 如果消息中指定了语言且支持，则使用指定的语言
	if msgLang != "" && IsLanguageSupported(msgLang) {
		return msgLang
	}

	// 否则返回默认语言
	return consts.DefaultLangCode
}

// LanguageMiddleware 语言检测中间件
func LanguageMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 自动检测并缓存语言设置
		GetLanguage(c)
		c.Next()
	}
}
