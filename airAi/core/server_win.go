//go:build windows
// +build windows

package core

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

func initServer(address string, router *gin.Engine) server {
	return &http.Server{
		Addr:           address,
		Handler:        router,
		ReadTimeout:    120 * time.Second, // 增加读取超时到120秒，支持长时间流式响应
		WriteTimeout:   120 * time.Second, // 增加写入超时到120秒，支持长时间流式响应
		MaxHeaderBytes: 1 << 20,
	}
}
