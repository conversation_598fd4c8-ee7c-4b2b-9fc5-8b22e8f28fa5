# airAi项目Makefile
# 支持多平台构建和开发工具集成

# 项目信息
PROJECT_NAME := airAi
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME := $(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建配置
GO_VERSION := 1.21
BINARY_NAME := $(PROJECT_NAME)
MAIN_PATH := .
BUILD_DIR := build
DIST_DIR := dist

# 平台配置
PLATFORMS := linux/amd64 linux/arm64 darwin/amd64 darwin/arm64 windows/amd64
DOCKER_IMAGE := $(PROJECT_NAME)
DOCKER_TAG := $(VERSION)

# Go构建标志
LDFLAGS := -X main.Version=$(VERSION) \
           -X main.BuildTime=$(BUILD_TIME) \
           -X main.GitCommit=$(GIT_COMMIT) \
           -w -s

# 颜色输出
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
RESET := \033[0m

.PHONY: help build run test clean deps fmt lint vet check docker-build docker-run cross-compile install uninstall dev dev-test dev-env run-test run-env build-linux build-linux-amd64 build-linux-arm64

# 默认目标
all: check build

# 显示帮助信息
help:
	@echo "$(BLUE)airAi项目构建工具$(RESET)"
	@echo ""
	@echo "$(GREEN)可用命令:$(RESET)"
	@echo "  $(YELLOW)build$(RESET)          - 构建项目"
	@echo "  $(YELLOW)run$(RESET)            - 运行项目"
	@echo "  $(YELLOW)test$(RESET)           - 运行测试"
	@echo "  $(YELLOW)clean$(RESET)          - 清理构建文件"
	@echo "  $(YELLOW)deps$(RESET)           - 安装依赖"
	@echo "  $(YELLOW)fmt$(RESET)            - 格式化代码"
	@echo "  $(YELLOW)lint$(RESET)           - 代码检查"
	@echo "  $(YELLOW)vet$(RESET)            - Go vet检查"
	@echo "  $(YELLOW)check$(RESET)          - 运行所有检查"
	@echo "  $(YELLOW)cross-compile$(RESET)  - 交叉编译所有平台"
	@echo "  $(YELLOW)docker-build$(RESET)   - 构建Docker镜像"
	@echo "  $(YELLOW)docker-run$(RESET)     - 运行Docker容器"
	@echo "  $(YELLOW)install$(RESET)        - 安装到系统"
	@echo "  $(YELLOW)uninstall$(RESET)      - 从系统卸载"
	@echo "  $(YELLOW)dev$(RESET)            - 开发模式运行"
	@echo "  $(YELLOW)dev-test$(RESET)       - 使用测试配置运行"
	@echo "  $(YELLOW)dev-env$(RESET)        - 使用.env环境变量运行"
	@echo "  $(YELLOW)run-test$(RESET)       - 使用config.test.yaml运行"
	@echo "  $(YELLOW)run-env$(RESET)        - 使用环境变量配置运行"
	@echo "  $(YELLOW)build-linux$(RESET)    - 构建所有Linux版本"
	@echo "  $(YELLOW)build-linux-amd64$(RESET) - 构建Linux AMD64版本"
	@echo "  $(YELLOW)build-linux-arm64$(RESET) - 构建Linux ARM64版本"
	@echo ""
	@echo "$(GREEN)环境变量:$(RESET)"
	@echo "  $(YELLOW)VERSION$(RESET)        - 版本号 (当前: $(VERSION))"
	@echo "  $(YELLOW)BUILD_DIR$(RESET)      - 构建目录 (当前: $(BUILD_DIR))"
	@echo "  $(YELLOW)GVA_CONFIG$(RESET)     - 配置文件路径"

# 构建项目
build:
	@echo "$(GREEN)构建 $(PROJECT_NAME) v$(VERSION)...$(RESET)"
	@mkdir -p $(BUILD_DIR)
	@go build -ldflags "$(LDFLAGS)" -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "$(GREEN)构建完成: $(BUILD_DIR)/$(BINARY_NAME)$(RESET)"

# 运行项目
run:
	@echo "$(GREEN)运行 $(PROJECT_NAME)...$(RESET)"
	@go run $(MAIN_PATH)

# 开发模式运行（带热重载）
dev:
	@echo "$(GREEN)开发模式运行 $(PROJECT_NAME)...$(RESET)"
	@if command -v air >/dev/null 2>&1; then \
		air; \
	else \
		echo "$(YELLOW)air未安装，使用普通模式运行$(RESET)"; \
		go run $(MAIN_PATH); \
	fi

# 使用测试配置运行（Gin测试模式）
dev-test:
	@echo "$(GREEN)使用测试配置运行 $(PROJECT_NAME)...$(RESET)"
	@echo "$(YELLOW)配置文件: config.test.yaml$(RESET)"
	@GIN_MODE=test go run $(MAIN_PATH)

# 使用.env环境变量运行
dev-env:
	@echo "$(GREEN)使用.env环境变量运行 $(PROJECT_NAME)...$(RESET)"
	@if [ -f ".env" ]; then \
		echo "$(YELLOW)加载 .env 文件...$(RESET)"; \
		export $$(grep -v '^#' .env | xargs) && go run $(MAIN_PATH); \
	else \
		echo "$(RED).env 文件不存在，请先创建$(RESET)"; \
		exit 1; \
	fi

# 使用config.test.yaml运行
run-test:
	@echo "$(GREEN)使用config.test.yaml运行 $(PROJECT_NAME)...$(RESET)"
	@go run $(MAIN_PATH) -c config.test.yaml

# 使用环境变量配置运行
run-env:
	@echo "$(GREEN)使用环境变量配置运行 $(PROJECT_NAME)...$(RESET)"
	@GVA_CONFIG=config.test.yaml go run $(MAIN_PATH)

# 运行测试
test:
	@echo "$(GREEN)运行测试...$(RESET)"
	@go test -v -race -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "$(GREEN)测试完成，覆盖率报告: coverage.html$(RESET)"

# 安装依赖
deps:
	@echo "$(GREEN)安装依赖...$(RESET)"
	@go mod download
	@go mod tidy
	@echo "$(GREEN)依赖安装完成$(RESET)"

# 格式化代码
fmt:
	@echo "$(GREEN)格式化代码...$(RESET)"
	@go fmt ./...
	@if command -v goimports >/dev/null 2>&1; then \
		goimports -w .; \
	fi
	@echo "$(GREEN)代码格式化完成$(RESET)"

# 代码检查
lint:
	@echo "$(GREEN)运行代码检查...$(RESET)"
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "$(YELLOW)golangci-lint未安装，跳过检查$(RESET)"; \
	fi

# Go vet检查
vet:
	@echo "$(GREEN)运行Go vet检查...$(RESET)"
	@go vet ./...

# 运行所有检查
check: fmt vet lint
	@echo "$(GREEN)所有检查完成$(RESET)"

# 交叉编译
cross-compile:
	@echo "$(GREEN)交叉编译所有平台...$(RESET)"
	@mkdir -p $(DIST_DIR)
	@for platform in $(PLATFORMS); do \
		os=$$(echo $$platform | cut -d'/' -f1); \
		arch=$$(echo $$platform | cut -d'/' -f2); \
		output_name=$(BINARY_NAME)-$$os-$$arch; \
		if [ $$os = "windows" ]; then output_name=$$output_name.exe; fi; \
		echo "构建 $$os/$$arch..."; \
		GOOS=$$os GOARCH=$$arch go build -ldflags "$(LDFLAGS)" -o $(DIST_DIR)/$$output_name $(MAIN_PATH); \
	done
	@echo "$(GREEN)交叉编译完成，文件位于 $(DIST_DIR)/$(RESET)"

# 构建Docker镜像
docker-build:
	@echo "$(GREEN)构建Docker镜像 $(DOCKER_IMAGE):$(DOCKER_TAG)...$(RESET)"
	@docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .
	@docker tag $(DOCKER_IMAGE):$(DOCKER_TAG) $(DOCKER_IMAGE):latest
	@echo "$(GREEN)Docker镜像构建完成$(RESET)"

# 运行Docker容器
docker-run:
	@echo "$(GREEN)运行Docker容器...$(RESET)"
	@docker run --rm -p 8888:8888 $(DOCKER_IMAGE):$(DOCKER_TAG)

# 安装到系统
install: build
	@echo "$(GREEN)安装 $(PROJECT_NAME) 到系统...$(RESET)"
	@sudo cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/
	@echo "$(GREEN)安装完成$(RESET)"

# 从系统卸载
uninstall:
	@echo "$(GREEN)从系统卸载 $(PROJECT_NAME)...$(RESET)"
	@sudo rm -f /usr/local/bin/$(BINARY_NAME)
	@echo "$(GREEN)卸载完成$(RESET)"

# 清理构建文件
clean:
	@echo "$(GREEN)清理构建文件...$(RESET)"
	@rm -rf $(BUILD_DIR) $(DIST_DIR)
	@rm -f coverage.out coverage.html
	@go clean
	@echo "$(GREEN)清理完成$(RESET)"

# 显示项目信息
info:
	@echo "$(BLUE)项目信息:$(RESET)"
	@echo "  名称: $(PROJECT_NAME)"
	@echo "  版本: $(VERSION)"
	@echo "  构建时间: $(BUILD_TIME)"
	@echo "  Git提交: $(GIT_COMMIT)"
	@echo "  Go版本: $(shell go version)"

# 检查工具安装
check-tools:
	@echo "$(GREEN)检查开发工具...$(RESET)"
	@echo -n "Go: "; if command -v go >/dev/null 2>&1; then echo "$(GREEN)✓$(RESET)"; else echo "$(RED)✗$(RESET)"; fi
	@echo -n "Git: "; if command -v git >/dev/null 2>&1; then echo "$(GREEN)✓$(RESET)"; else echo "$(RED)✗$(RESET)"; fi
	@echo -n "Docker: "; if command -v docker >/dev/null 2>&1; then echo "$(GREEN)✓$(RESET)"; else echo "$(RED)✗$(RESET)"; fi
	@echo -n "golangci-lint: "; if command -v golangci-lint >/dev/null 2>&1; then echo "$(GREEN)✓$(RESET)"; else echo "$(RED)✗$(RESET)"; fi
	@echo -n "goimports: "; if command -v goimports >/dev/null 2>&1; then echo "$(GREEN)✓$(RESET)"; else echo "$(RED)✗$(RESET)"; fi
	@echo -n "air: "; if command -v air >/dev/null 2>&1; then echo "$(GREEN)✓$(RESET)"; else echo "$(RED)✗$(RESET)"; fi

# 安装开发工具
install-tools:
	@echo "$(GREEN)安装开发工具...$(RESET)"
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@go install golang.org/x/tools/cmd/goimports@latest
	@go install github.com/cosmtrek/air@latest
	@echo "$(GREEN)开发工具安装完成$(RESET)"

# 构建所有Linux版本
build-linux:
	@echo "$(GREEN)构建所有Linux版本...$(RESET)"
	@mkdir -p $(DIST_DIR)
	@echo "构建 Linux AMD64..."
	@GOOS=linux GOARCH=amd64 go build -ldflags "$(LDFLAGS)" -o $(DIST_DIR)/$(BINARY_NAME)-linux-amd64 $(MAIN_PATH)
	@echo "构建 Linux ARM64..."
	@GOOS=linux GOARCH=arm64 go build -ldflags "$(LDFLAGS)" -o $(DIST_DIR)/$(BINARY_NAME)-linux-arm64 $(MAIN_PATH)
	@echo "$(GREEN)Linux版本构建完成，文件位于 $(DIST_DIR)/$(RESET)"
	@ls -la $(DIST_DIR)/$(BINARY_NAME)-linux-*

# 构建Linux AMD64版本
build-linux-amd64:
	@echo "$(GREEN)构建Linux AMD64版本...$(RESET)"
	@mkdir -p $(DIST_DIR)
	@GOOS=linux GOARCH=amd64 go build -ldflags "$(LDFLAGS)" -o $(DIST_DIR)/$(BINARY_NAME)-linux-amd64 $(MAIN_PATH)
	@echo "$(GREEN)构建完成: $(DIST_DIR)/$(BINARY_NAME)-linux-amd64$(RESET)"

# 构建Linux ARM64版本
build-linux-arm64:
	@echo "$(GREEN)构建Linux ARM64版本...$(RESET)"
	@mkdir -p $(DIST_DIR)
	@GOOS=linux GOARCH=arm64 go build -ldflags "$(LDFLAGS)" -o $(DIST_DIR)/$(BINARY_NAME)-linux-arm64 $(MAIN_PATH)
	@echo "$(GREEN)构建完成: $(DIST_DIR)/$(BINARY_NAME)-linux-arm64$(RESET)"
