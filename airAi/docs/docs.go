// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/fileUploadAndDownload/upload": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaFileUploadAndDownload"
                ],
                "summary": "上传文件示例",
                "parameters": [
                    {
                        "type": "file",
                        "description": "上传文件示例",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "上传文件示例,返回包括文件详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.ExaFileResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/bindPhone": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "绑定手机号",
                "parameters": [
                    {
                        "description": "phone",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.BindPhone"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "绑定手机号",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/chat": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "聊天推送 websocket",
                "responses": {
                    "200": {
                        "description": "返回消息返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/forgotPassword": {
            "post": {
                "description": "通过手机号或邮箱重置用户密码，需要验证码验证",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "忘记密码",
                "parameters": [
                    {
                        "description": "忘记密码请求，包含账号和验证码",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "密码重置成功，返回用户信息和token",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.LoginResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "验证码错误或已过期",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/v1/getCode": {
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Exchange"
                ],
                "summary": "发送验证码",
                "parameters": [
                    {
                        "description": "手机号, 密码, 验证码",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.RegisterCode"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回包括用户信息,token,过期时间",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/v1/googleLogin": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "Google登陆",
                "parameters": [
                    {
                        "description": "phone",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/airpods.AirpodsUser"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Google登陆",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.Response"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/logOut": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "退出登陆",
                "responses": {
                    "200": {
                        "description": "上传文件示例,返回包括文件详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/login": {
            "post": {
                "description": "支持手机号/邮箱+密码登录或验证码登录两种方式",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "用户登录",
                "parameters": [
                    {
                        "description": "登录请求，包含账号、密码或验证码",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.LoginRequest"
                        }
                    },
                    {
                        "type": "string",
                        "default": "zh_CN",
                        "description": "语言设置，支持zh_CN、en、id、hi",
                        "name": "Accept-Language",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功，返回用户信息和token",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.LoginResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "账号或密码错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "403": {
                        "description": "账号已被冻结",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/v1/recognize": {
            "post": {
                "description": "上传音频文件进行语音识别，支持多种音频格式，返回识别的文字内容",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "语音识别转文字",
                "parameters": [
                    {
                        "type": "file",
                        "description": "音频文件，支持wav、mp3、m4a等格式，文件大小限制10MB",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "zh_CN",
                        "description": "语言设置，影响识别准确度",
                        "name": "Accept-Language",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "识别成功，返回文字内容",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.RealTime"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "文件格式不支持或文件损坏",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "413": {
                        "description": "文件大小超出限制",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "422": {
                        "description": "音频内容无法识别",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "语音识别服务错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/v1/register": {
            "post": {
                "description": "通过手机号或邮箱注册新用户账号，需要验证码验证",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "用户注册",
                "parameters": [
                    {
                        "description": "注册请求，包含手机号/邮箱、密码、确认密码、验证码",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.Register"
                        }
                    },
                    {
                        "type": "string",
                        "default": "zh_CN",
                        "description": "语言设置，支持zh_CN、en、id、hi",
                        "name": "Accept-Language",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "注册成功，返回用户信息和token",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.LoginResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误或密码不匹配",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "409": {
                        "description": "用户已存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "422": {
                        "description": "验证码错误或已过期",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/v1/streamChat": {
            "post": {
                "description": "此端点已弃用，请使用 /v1/syncChat 并设置 stream=true 参数",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "text/event-stream"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "流式聊天对话 (已弃用)",
                "deprecated": true,
                "parameters": [
                    {
                        "description": "聊天请求",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.ChatRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "流式响应数据",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/v1/syncChat": {
            "post": {
                "description": "根据请求中的stream字段决定返回方式：stream=true时返回SSE流式响应，stream=false或未设置时返回标准JSON响应",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json",
                    "text/event-stream"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "统一聊天接口",
                "parameters": [
                    {
                        "description": "聊天请求，支持stream字段控制响应方式",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.ChatRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "流式响应：Server-Sent Events数据流",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/v1/tts": {
            "post": {
                "description": "将输入文本转换为MP3格式的语音音频文件，支持多种语音参数配置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "文字转语音",
                "parameters": [
                    {
                        "description": "文字转语音请求，包含要转换的文本内容",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.ChatRequest"
                        }
                    },
                    {
                        "type": "string",
                        "default": "zh_CN",
                        "description": "语言设置，影响语音合成语言",
                        "name": "Accept-Language",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回MP3格式音频数据的字节数组",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.TtsResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误或文本为空",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "认证失败",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "413": {
                        "description": "文本长度超出限制",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "语音合成服务错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/v1/updateUser": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "更新用户信息",
                "parameters": [
                    {
                        "description": "phone",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/airpods.AirpodsUser"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新用户信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "airpods.AirpodsUser": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "authorityId": {
                    "description": "用户角色ID",
                    "type": "integer"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "email": {
                    "description": "用户邮箱",
                    "type": "string"
                },
                "enable": {
                    "description": "用户是否被冻结 1正常 2冻结",
                    "type": "integer"
                },
                "headerImg": {
                    "description": "用户头像",
                    "type": "string"
                },
                "nickName": {
                    "description": "用户昵称",
                    "type": "string"
                },
                "originSetting": {
                    "description": "配置",
                    "allOf": [
                        {
                            "$ref": "#/definitions/common.JSONMap"
                        }
                    ]
                },
                "phone": {
                    "description": "用户手机号",
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                },
                "userName": {
                    "description": "用户登录名",
                    "type": "string"
                },
                "uuid": {
                    "description": "用户UUID",
                    "type": "string"
                }
            }
        },
        "common.JSONMap": {
            "type": "object",
            "additionalProperties": true
        },
        "example.ExaFileUploadAndDownload": {
            "type": "object",
            "properties": {
                "key": {
                    "description": "编号",
                    "type": "string"
                },
                "name": {
                    "description": "文件名",
                    "type": "string"
                },
                "tag": {
                    "description": "文件标签",
                    "type": "string"
                },
                "url": {
                    "description": "文件地址",
                    "type": "string"
                }
            }
        },
        "request.Register": {
            "type": "object",
            "properties": {
                "account": {
                    "description": "手机号/邮箱",
                    "type": "string",
                    "example": "手机号/邮箱"
                },
                "code": {
                    "description": "验证码",
                    "type": "string",
                    "example": "验证码"
                },
                "confirmPassWord": {
                    "description": "确认密码",
                    "type": "string",
                    "example": "确认密码"
                },
                "password": {
                    "description": "密码",
                    "type": "string",
                    "example": "密码"
                }
            }
        },
        "requst.BindPhone": {
            "type": "object",
            "required": [
                "phone"
            ],
            "properties": {
                "phone": {
                    "description": "手机号",
                    "type": "string",
                    "example": "手机号"
                }
            }
        },
        "requst.ChatMessage": {
            "type": "object",
            "properties": {
                "content": {
                    "description": "消息内容",
                    "type": "string",
                    "example": "你好，请介绍一下Go语言"
                },
                "role": {
                    "description": "消息角色：user(用户) 或 assistant(AI助手)",
                    "type": "string",
                    "enum": [
                        "user",
                        "assistant"
                    ],
                    "example": "user"
                }
            }
        },
        "requst.ChatRequest": {
            "type": "object",
            "required": [
                "text"
            ],
            "properties": {
                "context": {
                    "description": "对话上下文历史消息列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/requst.ChatMessage"
                    }
                },
                "conversation_id": {
                    "description": "对话ID，用于多轮对话上下文管理",
                    "type": "string",
                    "example": "conv_123456"
                },
                "language": {
                    "description": "语言设置，支持中文、英文、印尼语、印地语",
                    "type": "string",
                    "enum": [
                        "zh_CN",
                        "en",
                        "id",
                        "hi"
                    ],
                    "example": "zh_CN"
                },
                "max_history": {
                    "description": "最大历史消息数量，范围1-50，默认10",
                    "type": "integer",
                    "maximum": 50,
                    "minimum": 1,
                    "example": 10
                },
                "stream": {
                    "description": "是否使用流式响应，true为SSE流式，false为同步响应",
                    "type": "boolean",
                    "example": true
                },
                "text": {
                    "description": "用户输入文本，必填",
                    "type": "string",
                    "example": "go实现一个加法计算器"
                }
            }
        },
        "requst.LoginRequest": {
            "type": "object",
            "properties": {
                "account": {
                    "description": "手机号或者邮箱",
                    "type": "string"
                },
                "code": {
                    "description": "使用验证码登陆时填",
                    "type": "string"
                },
                "password": {
                    "description": "使用密码时填写",
                    "type": "string"
                }
            }
        },
        "requst.RegisterCode": {
            "type": "object",
            "required": [
                "account"
            ],
            "properties": {
                "account": {
                    "description": "账号",
                    "type": "string",
                    "example": "账号"
                },
                "code": {
                    "description": "验证码",
                    "type": "integer",
                    "example": 12345
                }
            }
        },
        "response.ExaFileResponse": {
            "type": "object",
            "properties": {
                "file": {
                    "$ref": "#/definitions/example.ExaFileUploadAndDownload"
                }
            }
        },
        "response.LoginResponse": {
            "type": "object",
            "properties": {
                "expiresAt": {
                    "description": "token过期时间",
                    "type": "integer"
                },
                "token": {
                    "description": "登陆token",
                    "type": "string"
                },
                "user": {
                    "description": "用户",
                    "allOf": [
                        {
                            "$ref": "#/definitions/response.User"
                        }
                    ]
                }
            }
        },
        "response.RealTime": {
            "type": "object",
            "properties": {
                "text": {
                    "description": "识别出的文本内容",
                    "type": "string",
                    "example": "你好，这是语音识别的结果"
                }
            }
        },
        "response.Response": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "msg": {
                    "type": "string"
                }
            }
        },
        "response.TtsResponse": {
            "type": "object",
            "properties": {
                "output": {
                    "description": "MP3格式音频数据的字节数组",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "response.User": {
            "type": "object",
            "properties": {
                "email": {
                    "description": "用户邮箱",
                    "type": "string"
                },
                "enable": {
                    "description": "用户",
                    "type": "integer"
                },
                "headerImg": {
                    "description": "用户头像",
                    "type": "string"
                },
                "nickName": {
                    "description": "用户昵称",
                    "type": "string"
                },
                "phone": {
                    "description": "用户手机号",
                    "type": "string"
                },
                "userName": {
                    "description": "用户登录名",
                    "type": "string"
                },
                "uuid": {
                    "description": "用户UUID",
                    "type": "string"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "",
	Description:      "",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
