# airAi API Swagger 注释模板

本文档提供了airAi项目中API接口的标准Swagger注释模板，确保文档的一致性和完整性。

## 📋 基本模板结构

### 1. 完整的API接口注释模板

```go
// FunctionName 接口功能简述
// @Tags     分组名称
// @Summary  接口简要说明
// @Description 详细的接口描述，包括功能、使用场景、注意事项等
// @Security ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     参数名  参数位置  参数类型  是否必填  "参数描述"  其他约束
// @Success  200   {object}  response.Response{data=具体数据类型,msg=string}  "成功响应描述"
// @Failure  400   {object}  response.Response  "请求参数错误"
// @Failure  401   {object}  response.Response  "认证失败"
// @Failure  500   {object}  response.Response  "服务器内部错误"
// @Router   /v1/接口路径 [请求方法]
```

### 2. 参数位置说明

- `body` - 请求体参数（JSON）
- `formData` - 表单参数（multipart/form-data）
- `query` - URL查询参数
- `path` - 路径参数
- `header` - 请求头参数

### 3. 常用Tags分类

- `Ear` - 核心AI功能接口（聊天、语音识别、TTS等）
- `Exchange` - 通用服务接口（验证码、文件上传等）
- `Auth` - 认证相关接口

## 🎯 具体接口类型模板

### 聊天接口模板

```go
// SyncChat 统一聊天接口
// @Tags     Ear
// @Summary  统一聊天对话接口
// @Description 支持流式(SSE)和同步聊天响应。当stream=true时返回Server-Sent Events流式数据；当stream=false时返回完整响应。支持多轮对话上下文管理和国际化。
// @accept    application/json
// @Produce   application/json
// @Produce   text/event-stream
// @Param     data  body    requst.ChatRequest           true  "聊天请求，包含用户输入文本、对话ID、上下文等"
// @Param     Accept-Language  header  string  false  "语言设置，支持zh_CN、en、id、hi"  default(zh_CN)
// @Success  200   {object}  response.Response{data=string,msg=string}  "同步模式：返回完整聊天响应"
// @Success  200   {string}  string  "流式模式：返回SSE事件流，包含data、heartbeat、complete、error事件"
// @Failure  400   {object}  response.Response  "请求参数错误或验证失败"
// @Failure  401   {object}  response.Response  "认证失败或token无效"
// @Failure  429   {object}  response.Response  "请求频率限制"
// @Failure  500   {object}  response.Response  "AI服务错误或服务器内部错误"
// @Router   /v1/syncChat [post]
```

### 文件上传接口模板

```go
// Recognize 语音识别接口
// @Tags     Ear
// @Summary  语音识别转文字
// @Description 上传音频文件进行语音识别，支持多种音频格式，返回识别的文字内容
// @accept    multipart/form-data
// @Produce   application/json
// @Param     file  formData  file true  "音频文件，支持wav、mp3、m4a等格式，文件大小限制10MB"
// @Param     Accept-Language  header  string  false  "语言设置，影响识别准确度"  default(zh_CN)
// @Success  200   {object}  response.Response{data=airRsq.RealTime,msg=string}  "识别成功，返回文字内容"
// @Failure  400   {object}  response.Response  "文件格式不支持或文件损坏"
// @Failure  413   {object}  response.Response  "文件大小超出限制"
// @Failure  422   {object}  response.Response  "音频内容无法识别"
// @Failure  500   {object}  response.Response  "语音识别服务错误"
// @Router   /v1/recognize [post]
```

### 认证接口模板

```go
// Login 用户登录接口
// @Tags      Ear
// @Summary   用户登录
// @Description 支持手机号/邮箱+密码登录或验证码登录两种方式
// @accept    application/json
// @Produce   application/json
// @Param     data  body    requst.LoginRequest           true  "登录请求，包含账号、密码或验证码"
// @Param     Accept-Language  header  string  false  "语言设置，支持zh_CN、en、id、hi"  default(zh_CN)
// @Success   200   {object}  response.Response{data=res.LoginResponse,msg=string}  "登录成功，返回用户信息和token"
// @Failure   400   {object}  response.Response  "请求参数错误"
// @Failure   401   {object}  response.Response  "账号或密码错误"
// @Failure   403   {object}  response.Response  "账号已被冻结"
// @Failure   500   {object}  response.Response  "服务器内部错误"
// @Router    /v1/login [post]
```

## 📝 数据结构注释规范

### 请求结构体注释

```go
type ChatRequest struct {
    Text           string        `form:"text" json:"text" binding:"required" example:"go实现一个加法计算器"`                      // 用户输入文本，必填
    ConversationID string        `form:"conversation_id" json:"conversation_id,omitempty" example:"conv_123456"`         // 对话ID，用于多轮对话上下文管理
    Language       string        `form:"language" json:"language,omitempty" example:"zh_CN" enums:"zh_CN,en,id,hi"`      // 语言设置，支持中文、英文、印尼语、印地语
    MaxHistory     int           `form:"max_history" json:"max_history,omitempty" example:"10" minimum:"1" maximum:"50"` // 最大历史消息数量，范围1-50，默认10
    Stream         bool          `form:"stream" json:"stream,omitempty" example:"true"`                                  // 是否使用流式响应，true为SSE流式，false为同步响应
}
```

### 响应结构体注释

```go
type RealTime struct {
    Text string `json:"text" example:"你好，这是语音识别的结果"` // 识别出的文本内容
}

type TtsResponse struct {
    Output []byte `json:"output" example:"[137,80,78,71...]"` // MP3格式音频数据的字节数组
}
```

## 🔧 常用标签说明

### Swagger标签

- `example` - 示例值
- `enums` - 枚举值列表
- `minimum/maximum` - 数值范围
- `default` - 默认值
- `binding:"required"` - 必填字段

### HTTP状态码规范

- `200` - 成功
- `400` - 请求参数错误
- `401` - 认证失败
- `403` - 权限不足
- `404` - 资源不存在
- `413` - 请求体过大
- `422` - 请求格式正确但语义错误
- `429` - 请求频率限制
- `500` - 服务器内部错误

## ✅ 检查清单

在编写API文档时，请确保包含以下要素：

- [ ] @Tags 分组标签
- [ ] @Summary 简要说明
- [ ] @Description 详细描述
- [ ] @Param 所有参数（包括Accept-Language）
- [ ] @Success 成功响应
- [ ] @Failure 错误响应（至少包含400、401、500）
- [ ] @Router 正确的路由路径
- [ ] 数据结构包含example和约束
- [ ] 中文注释保持一致性

## 🌐 国际化支持

所有接口都应支持Accept-Language头部参数：

```go
// @Param     Accept-Language  header  string  false  "语言设置，支持zh_CN、en、id、hi"  default(zh_CN)
```

支持的语言代码：
- `zh_CN` - 简体中文
- `en` - 英语
- `id` - 印尼语
- `hi` - 印地语
