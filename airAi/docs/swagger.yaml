definitions:
  airpods.AirpodsUser:
    properties:
      ID:
        description: 主键ID
        type: integer
      authorityId:
        description: 用户角色ID
        type: integer
      createdAt:
        description: 创建时间
        type: string
      email:
        description: 用户邮箱
        type: string
      enable:
        description: 用户是否被冻结 1正常 2冻结
        type: integer
      headerImg:
        description: 用户头像
        type: string
      nickName:
        description: 用户昵称
        type: string
      originSetting:
        allOf:
        - $ref: '#/definitions/common.JSONMap'
        description: 配置
      phone:
        description: 用户手机号
        type: string
      updatedAt:
        description: 更新时间
        type: string
      userName:
        description: 用户登录名
        type: string
      uuid:
        description: 用户UUID
        type: string
    type: object
  common.JSONMap:
    additionalProperties: true
    type: object
  example.ExaFileUploadAndDownload:
    properties:
      key:
        description: 编号
        type: string
      name:
        description: 文件名
        type: string
      tag:
        description: 文件标签
        type: string
      url:
        description: 文件地址
        type: string
    type: object
  request.Register:
    properties:
      account:
        description: 手机号/邮箱
        example: 手机号/邮箱
        type: string
      code:
        description: 验证码
        example: 验证码
        type: string
      confirmPassWord:
        description: 确认密码
        example: 确认密码
        type: string
      password:
        description: 密码
        example: 密码
        type: string
    type: object
  requst.BindPhone:
    properties:
      phone:
        description: 手机号
        example: 手机号
        type: string
    required:
    - phone
    type: object
  requst.ChatMessage:
    properties:
      content:
        description: 消息内容
        example: 你好，请介绍一下Go语言
        type: string
      role:
        description: 消息角色：user(用户) 或 assistant(AI助手)
        enum:
        - user
        - assistant
        example: user
        type: string
    type: object
  requst.ChatRequest:
    properties:
      context:
        description: 对话上下文历史消息列表
        items:
          $ref: '#/definitions/requst.ChatMessage'
        type: array
      conversation_id:
        description: 对话ID，用于多轮对话上下文管理
        example: conv_123456
        type: string
      language:
        description: 语言设置，支持中文、英文、印尼语、印地语
        enum:
        - zh_CN
        - en
        - id
        - hi
        example: zh_CN
        type: string
      max_history:
        description: 最大历史消息数量，范围1-50，默认10
        example: 10
        maximum: 50
        minimum: 1
        type: integer
      stream:
        description: 是否使用流式响应，true为SSE流式，false为同步响应
        example: true
        type: boolean
      text:
        description: 用户输入文本，必填
        example: go实现一个加法计算器
        type: string
    required:
    - text
    type: object
  requst.LoginRequest:
    properties:
      account:
        description: 手机号或者邮箱
        type: string
      code:
        description: 使用验证码登陆时填
        type: string
      password:
        description: 使用密码时填写
        type: string
    type: object
  requst.RegisterCode:
    properties:
      account:
        description: 账号
        example: 账号
        type: string
      code:
        description: 验证码
        example: 12345
        type: integer
    required:
    - account
    type: object
  response.ExaFileResponse:
    properties:
      file:
        $ref: '#/definitions/example.ExaFileUploadAndDownload'
    type: object
  response.LoginResponse:
    properties:
      expiresAt:
        description: token过期时间
        type: integer
      token:
        description: 登陆token
        type: string
      user:
        allOf:
        - $ref: '#/definitions/response.User'
        description: 用户
    type: object
  response.RealTime:
    properties:
      text:
        description: 识别出的文本内容
        example: 你好，这是语音识别的结果
        type: string
    type: object
  response.Response:
    properties:
      code:
        type: integer
      data: {}
      msg:
        type: string
    type: object
  response.TtsResponse:
    properties:
      output:
        description: MP3格式音频数据的字节数组
        items:
          type: integer
        type: array
    type: object
  response.User:
    properties:
      email:
        description: 用户邮箱
        type: string
      enable:
        description: 用户
        type: integer
      headerImg:
        description: 用户头像
        type: string
      nickName:
        description: 用户昵称
        type: string
      phone:
        description: 用户手机号
        type: string
      userName:
        description: 用户登录名
        type: string
      uuid:
        description: 用户UUID
        type: string
    type: object
info:
  contact: {}
paths:
  /fileUploadAndDownload/upload:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 上传文件示例
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 上传文件示例,返回包括文件详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ExaFileResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 上传文件示例
      tags:
      - ExaFileUploadAndDownload
  /v1/bindPhone:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: phone
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.BindPhone'
      produces:
      - application/json
      responses:
        "200":
          description: 绑定手机号
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 绑定手机号
      tags:
      - Ear
  /v1/chat:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: 返回消息返回数据
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
                msg:
                  type: string
              type: object
      summary: 聊天推送 websocket
      tags:
      - Ear
  /v1/forgotPassword:
    post:
      consumes:
      - application/json
      description: 通过手机号或邮箱重置用户密码，需要验证码验证
      parameters:
      - description: 忘记密码请求，包含账号和验证码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 密码重置成功，返回用户信息和token
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.LoginResponse'
                msg:
                  type: string
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: 验证码错误或已过期
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 忘记密码
      tags:
      - Ear
  /v1/getCode:
    post:
      parameters:
      - description: 手机号, 密码, 验证码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.RegisterCode'
      produces:
      - application/json
      responses:
        "200":
          description: 返回包括用户信息,token,过期时间
          schema:
            $ref: '#/definitions/response.Response'
      summary: 发送验证码
      tags:
      - Exchange
  /v1/googleLogin:
    post:
      consumes:
      - application/json
      parameters:
      - description: phone
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/airpods.AirpodsUser'
      produces:
      - application/json
      responses:
        "200":
          description: Google登陆
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.Response'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: Google登陆
      tags:
      - Ear
  /v1/logOut:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 上传文件示例,返回包括文件详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 退出登陆
      tags:
      - Ear
  /v1/login:
    post:
      consumes:
      - application/json
      description: 支持手机号/邮箱+密码登录或验证码登录两种方式
      parameters:
      - description: 登录请求，包含账号、密码或验证码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.LoginRequest'
      - default: zh_CN
        description: 语言设置，支持zh_CN、en、id、hi
        in: header
        name: Accept-Language
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功，返回用户信息和token
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.LoginResponse'
                msg:
                  type: string
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: 账号或密码错误
          schema:
            $ref: '#/definitions/response.Response'
        "403":
          description: 账号已被冻结
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 用户登录
      tags:
      - Ear
  /v1/recognize:
    post:
      consumes:
      - multipart/form-data
      description: 上传音频文件进行语音识别，支持多种音频格式，返回识别的文字内容
      parameters:
      - description: 音频文件，支持wav、mp3、m4a等格式，文件大小限制10MB
        in: formData
        name: file
        required: true
        type: file
      - default: zh_CN
        description: 语言设置，影响识别准确度
        in: header
        name: Accept-Language
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 识别成功，返回文字内容
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.RealTime'
                msg:
                  type: string
              type: object
        "400":
          description: 文件格式不支持或文件损坏
          schema:
            $ref: '#/definitions/response.Response'
        "413":
          description: 文件大小超出限制
          schema:
            $ref: '#/definitions/response.Response'
        "422":
          description: 音频内容无法识别
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 语音识别服务错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 语音识别转文字
      tags:
      - Ear
  /v1/register:
    post:
      consumes:
      - application/json
      description: 通过手机号或邮箱注册新用户账号，需要验证码验证
      parameters:
      - description: 注册请求，包含手机号/邮箱、密码、确认密码、验证码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.Register'
      - default: zh_CN
        description: 语言设置，支持zh_CN、en、id、hi
        in: header
        name: Accept-Language
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 注册成功，返回用户信息和token
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.LoginResponse'
                msg:
                  type: string
              type: object
        "400":
          description: 请求参数错误或密码不匹配
          schema:
            $ref: '#/definitions/response.Response'
        "409":
          description: 用户已存在
          schema:
            $ref: '#/definitions/response.Response'
        "422":
          description: 验证码错误或已过期
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 用户注册
      tags:
      - Ear
  /v1/streamChat:
    post:
      consumes:
      - application/json
      deprecated: true
      description: 此端点已弃用，请使用 /v1/syncChat 并设置 stream=true 参数
      parameters:
      - description: 聊天请求
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.ChatRequest'
      produces:
      - text/event-stream
      responses:
        "200":
          description: 流式响应数据
          schema:
            type: string
      summary: 流式聊天对话 (已弃用)
      tags:
      - Ear
  /v1/syncChat:
    post:
      consumes:
      - application/json
      description: 根据请求中的stream字段决定返回方式：stream=true时返回SSE流式响应，stream=false或未设置时返回标准JSON响应
      parameters:
      - description: 聊天请求，支持stream字段控制响应方式
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.ChatRequest'
      produces:
      - application/json
      - text/event-stream
      responses:
        "200":
          description: 流式响应：Server-Sent Events数据流
          schema:
            type: string
      summary: 统一聊天接口
      tags:
      - Ear
  /v1/tts:
    post:
      consumes:
      - application/json
      description: 将输入文本转换为MP3格式的语音音频文件，支持多种语音参数配置
      parameters:
      - description: 文字转语音请求，包含要转换的文本内容
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.ChatRequest'
      - default: zh_CN
        description: 语言设置，影响语音合成语言
        in: header
        name: Accept-Language
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回MP3格式音频数据的字节数组
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.TtsResponse'
                msg:
                  type: string
              type: object
        "400":
          description: 请求参数错误或文本为空
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/response.Response'
        "413":
          description: 文本长度超出限制
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 语音合成服务错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 文字转语音
      tags:
      - Ear
  /v1/updateUser:
    post:
      consumes:
      - application/json
      parameters:
      - description: phone
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/airpods.AirpodsUser'
      produces:
      - application/json
      responses:
        "200":
          description: 更新用户信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新用户信息
      tags:
      - Ear
swagger: "2.0"
