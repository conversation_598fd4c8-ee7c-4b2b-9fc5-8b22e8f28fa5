# airAi 本地开发配置指南

本指南介绍如何在本地开发环境中自动加载 `config.test.yaml` 配置文件，而不是默认的 `config.yaml`。

## 📋 配置加载机制

airAi 项目使用以下优先级加载配置文件：

**优先级顺序：命令行参数 > 环境变量 > Gin模式自动选择**

1. **命令行参数** (`-c`): 最高优先级
2. **环境变量** (`GVA_CONFIG`): 中等优先级  
3. **Gin模式自动选择**: 最低优先级
   - `gin.DebugMode` → `config.yaml`
   - `gin.TestMode` → `config.test.yaml`
   - `gin.ReleaseMode` → `config.release.yaml`

## 🎯 四种解决方案

### 方案1：使用 .env 环境变量文件（推荐）

#### 1.1 创建 .env 文件
```bash
# .env 文件已创建，内容如下：
GVA_CONFIG=config.test.yaml
```

#### 1.2 使用启动脚本
```bash
# 使用 dev.sh 脚本启动
./dev.sh
```

#### 1.3 手动加载环境变量
```bash
# 手动导出环境变量
export $(grep -v '^#' .env | xargs)
go run main.go
```

### 方案2：使用 Gin 测试模式

#### 2.1 使用启动脚本
```bash
# 使用 test.sh 脚本启动
./test.sh
```

#### 2.2 手动设置 Gin 模式
```bash
# 设置 Gin 为测试模式
export GIN_MODE=test
go run main.go
```

### 方案3：使用命令行参数

#### 3.1 使用启动脚本
```bash
# 使用 dev-config.sh 脚本启动
./dev-config.sh
```

#### 3.2 直接使用命令行参数
```bash
# 直接指定配置文件
go run main.go -c config.test.yaml
```

### 方案4：使用 Makefile（推荐用于团队开发）

```bash
# 查看所有可用命令
make help

# 使用测试配置运行（Gin测试模式）
make dev-test

# 使用 .env 环境变量运行
make dev-env

# 使用 config.test.yaml 运行（命令行参数）
make run-test

# 使用环境变量配置运行
make run-env
```

## 🔧 已创建的文件

### 1. 环境变量文件
- **`.env`**: 包含 `GVA_CONFIG=config.test.yaml`

### 2. 启动脚本
- **`dev.sh`**: 自动加载 .env 文件并启动项目
- **`test.sh`**: 设置 Gin 测试模式并启动项目  
- **`dev-config.sh`**: 使用命令行参数指定配置文件

### 3. Makefile 扩展
- **`make dev-test`**: 使用测试配置运行
- **`make dev-env`**: 使用 .env 环境变量运行
- **`make run-test`**: 使用 config.test.yaml 运行
- **`make run-env`**: 使用环境变量配置运行

## ✅ 验证配置加载

启动项目后，查看控制台输出中的配置文件路径信息：

```bash
# 环境变量方式
您正在使用GVA_CONFIG环境变量,config的路径为config.test.yaml

# Gin测试模式方式  
您正在使用gin模式的test环境名称,config的路径为config.test.yaml

# 命令行参数方式
您正在使用命令行的-c参数传递的值,config的路径为config.test.yaml
```

## 🚀 推荐使用方式

### 个人开发
```bash
# 最简单的方式
./dev.sh
```

### 团队开发
```bash
# 使用 Makefile，更规范
make dev-test
```

### CI/CD 环境
```bash
# 使用环境变量，更灵活
export GVA_CONFIG=config.test.yaml
go run main.go
```

## 📝 注意事项

1. **`.env` 文件**: 已添加到项目中，包含本地开发所需的环境变量
2. **脚本权限**: 所有 `.sh` 脚本已设置为可执行权限
3. **配置文件**: `config.test.yaml` 已存在，包含测试环境的配置
4. **环境变量名**: 使用 `GVA_CONFIG` 环境变量指定配置文件路径
5. **Gin模式**: 可通过 `GIN_MODE` 环境变量控制（debug/test/release）

## 🔍 故障排除

### 问题1: .env 文件不生效
```bash
# 检查文件是否存在
ls -la .env

# 检查文件内容
cat .env

# 手动加载环境变量
source .env
```

### 问题2: 脚本无法执行
```bash
# 设置执行权限
chmod +x dev.sh test.sh dev-config.sh
```

### 问题3: 配置文件未找到
```bash
# 检查配置文件是否存在
ls -la config.test.yaml

# 检查当前工作目录
pwd
```
