package requst

// ChatMessage 聊天消息结构
type ChatMessage struct {
	Role    string `json:"role" example:"user" enums:"user,assistant"` // 消息角色：user(用户) 或 assistant(AI助手)
	Content string `json:"content" example:"你好，请介绍一下Go语言"`             // 消息内容
}

// ChatRequest 聊天请求结构
type ChatRequest struct {
	Text           string        `form:"text" json:"text" binding:"required" example:"go实现一个加法计算器"`                      // 用户输入文本，必填
	ConversationID string        `form:"conversation_id" json:"conversation_id,omitempty" example:"conv_123456"`         // 对话ID，用于多轮对话上下文管理
	Context        []ChatMessage `form:"context" json:"context,omitempty"`                                               // 对话上下文历史消息列表
	Language       string        `form:"language" json:"language,omitempty" example:"zh_CN" enums:"zh_CN,en,id,hi"`      // 语言设置，支持中文、英文、印尼语、印地语
	MaxHistory     int           `form:"max_history" json:"max_history,omitempty" example:"10" minimum:"1" maximum:"50"` // 最大历史消息数量，范围1-50，默认10
	Stream         bool          `form:"stream" json:"stream,omitempty" example:"true"`                                  // 是否使用流式响应，true为SSE流式，false为同步响应
}

// TtsRequest 文字转语音请求结构
type TtsRequest struct {
	Text     string `json:"text" binding:"required"`     // 要转换的文本
	Language string `json:"language" binding:"required"` // 语言代码
}

// PurchaseQuotaRequest 购买配额请求结构
type PurchaseQuotaRequest struct {
	Units int `json:"units" binding:"required,min=1"` // 购买单位数（每单位50次）
}
