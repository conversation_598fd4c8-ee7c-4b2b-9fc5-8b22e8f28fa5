package requst

type LoginRequest struct {
	Account  string `json:"account"`            // 手机号或者邮箱
	Code     string `json:"code"`               // 使用验证码登陆时填
	Password string `json:"password,omitempty"` // 使用密码时填写
}

// RegisterCode User register structure
type RegisterCode struct {
	Code    int64  `json:"code" example:"012345" `                  // 验证码
	Account string `json:"account" binding:"required" example:"账号"` // 账号
}

// BindPhone User phone
type BindPhone struct {
	Phone string `json:"phone" binding:"required" example:"手机号"` // 手机号
}
