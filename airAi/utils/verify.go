package utils

var (
	LoginVerify              = Rules{"Account": {NotEmpty()}}
	ChatVerify               = Rules{"Text": {NotEmpty()}}
	BindPhoneVerify          = Rules{"Phone": {NotEmpty()}}
	RegisterVerify           = Rules{"Username": {NotEmpty()}, "Password": {NotEmpty()}, "ConfirmPassword": {NotEmpty()}, "Code": {NotEmpty()}}
	PageInfoVerify           = Rules{"Page": {NotEmpty()}, "PageSize": {NotEmpty()}}
	CustomerVerify           = Rules{"CustomerName": {NotEmpty()}, "CustomerPhoneData": {NotEmpty()}}
	AutoCodeVerify           = Rules{"Abbreviation": {NotEmpty()}, "StructName": {NotEmpty()}, "PackageName": {NotEmpty()}}
	AutoPackageVerify        = Rules{"PackageName": {NotEmpty()}}
	AuthorityVerify          = Rules{"AuthorityId": {NotEmpty()}, "AuthorityName": {NotEmpty()}}
	AuthorityIdVerify        = Rules{"AuthorityId": {NotEmpty()}}
	OldAuthorityVerify       = Rules{"OldAuthorityId": {NotEmpty()}}
	ChangePasswordVerify     = Rules{"Password": {NotEmpty()}, "NewPassword": {NotEmpty()}}
	SetUserAuthorityVerify   = Rules{"AuthorityId": {NotEmpty()}}
	TradeOrderVerify         = Rules{"Login": {NotEmpty()}, "Symbol": {NotEmpty()}, "Volume": {NotEmpty()}, "Type": {NotEmpty()}}
	ChangePasswordExcVerify  = Rules{"Login": {NotEmpty()}, "Password": {NotEmpty()}}
	UpdateOrderSlTpVerify    = Rules{"Login": {NotEmpty()}, "Symbol": {NotEmpty()}}
	LoginAccountVerify       = Rules{"Login": {NotEmpty()}}
	CreateLoginVerify        = Rules{"UseType": {NotEmpty()}}
	PushMessageAccountVerify = Rules{"Login": {NotEmpty()}, "MsgId": {NotEmpty()}}
	ExchangeLoginVerify      = Rules{"Account": {NotEmpty()}}
	KlineVerify              = Rules{"Symbol": {NotEmpty()}, "Interval": {NotEmpty()}}
	InterTransfer            = Rules{"FromLogin": {NotEmpty()}, "ToLogin": {NotEmpty()}, "Amount": {NotEmpty()}}
	DemoTransfer             = Rules{"Login": {NotEmpty()}, "Amount": {NotEmpty()}}
	TradePay                 = Rules{"Login": {NotEmpty()}, "Amount": {NotEmpty()}, "Channel": {NotEmpty()}}
	Withdrawal               = Rules{"Login": {NotEmpty()}, "Amount": {NotEmpty()}, "Symbol": {NotEmpty()}}
	Symbol                   = Rules{"Symbol": {NotEmpty()}}
	CheckLogin               = Rules{"Login": {NotEmpty()}, "Password": {NotEmpty()}}
	UpdatePassword           = Rules{"Code": {NotEmpty()}, "Password": {NotEmpty()}, "ComPassword": {NotEmpty()}}
	UpdateAccountPassword    = Rules{"Account": {NotEmpty()}, "Code": {NotEmpty()}, "Password": {NotEmpty()}, "ComPassword": {NotEmpty()}}
	UpdateNickname           = Rules{"Nickname": {NotEmpty()}}
	UpdateIntroduction       = Rules{"Introduction": {NotEmpty()}}
	UpdateAvatar             = Rules{"Avatar": {NotEmpty()}}
)
