package service

import (
	"context"
	"fmt"
	"time"

	"airAi/config"
	"airAi/global"

	"github.com/cloudwego/eino-ext/components/model/deepseek"
	"github.com/cloudwego/eino/schema"
	"go.uber.org/zap"
)

// EinoService Eino服务结构
type EinoService struct {
	chatModel *deepseek.ChatModel
	config    *config.DeepSeek
}

// NewEinoService 创建新的Eino服务实例
func NewEinoService() *EinoService {
	return &EinoService{
		config: &global.GVA_CONFIG.DeepSeek,
	}
}

// InitializeChatModel 初始化ChatModel
func (s *EinoService) InitializeChatModel(ctx context.Context) error {
	// 验证配置
	if s.config.APIKey == "" {
		global.GVA_LOG.Error("DeepSeek API密钥未配置")
		return fmt.Errorf("DeepSeek API密钥未配置")
	}

	// 创建官方Eino DeepSeek ChatModel实现
	config := &deepseek.ChatModelConfig{
		APIKey:      s.config.APIKey,
		Model:       s.config.Model,
		MaxTokens:   s.config.MaxTokens,
		Temperature: s.config.Temperature,
		TopP:        s.config.TopP,
	}

	// 设置BaseURL（如果配置了）
	if s.config.BaseURL != "" {
		config.BaseURL = s.config.BaseURL
	}

	chatModel, err := deepseek.NewChatModel(ctx, config)
	if err != nil {
		global.GVA_LOG.Error("初始化ChatModel失败",
			zap.Error(err),
			zap.String("model", s.config.Model),
			zap.String("base_url", s.config.BaseURL))
		return fmt.Errorf("failed to initialize ChatModel: %w", err)
	}

	s.chatModel = chatModel
	global.GVA_LOG.Info("ChatModel初始化成功",
		zap.String("model", s.config.Model),
		zap.String("base_url", s.config.BaseURL),
		zap.Int("max_tokens", s.config.MaxTokens))
	return nil
}

// GetChatModel 获取ChatModel实例
func (s *EinoService) GetChatModel() *deepseek.ChatModel {
	return s.chatModel
}

// GenerateResponse 生成聊天响应
func (s *EinoService) GenerateResponse(ctx context.Context, userMessage string) (string, error) {
	if s.chatModel == nil {
		global.GVA_LOG.Error("ChatModel未初始化，无法生成响应")
		return "", fmt.Errorf("ChatModel未初始化")
	}

	// 验证输入
	if userMessage == "" {
		global.GVA_LOG.Warn("用户消息为空")
		return "", fmt.Errorf("用户消息不能为空")
	}

	// 记录请求开始
	global.GVA_LOG.Debug("开始生成聊天响应",
		zap.String("user_message", userMessage),
		zap.String("model", s.config.Model))

	// 构建Eino消息格式
	messages := []*schema.Message{
		{
			Role:    schema.User,
			Content: userMessage,
		},
	}

	// 调用模型生成响应
	response, err := s.chatModel.Generate(ctx, messages)
	if err != nil {
		global.GVA_LOG.Error("生成响应失败",
			zap.Error(err),
			zap.String("user_message", userMessage),
			zap.String("model", s.config.Model))
		return "", fmt.Errorf("failed to generate response: %w", err)
	}

	// 提取响应内容
	responseContent := ""
	if response != nil && response.Content != "" {
		responseContent = response.Content
	}

	// 记录成功响应
	global.GVA_LOG.Debug("成功生成聊天响应",
		zap.String("user_message", userMessage),
		zap.Int("response_length", len(responseContent)))

	return responseContent, nil
}

// GenerateResponseWithContext 使用上下文生成聊天响应
func (s *EinoService) GenerateResponseWithContext(ctx context.Context, messages []*schema.Message) (string, error) {
	if s.chatModel == nil {
		global.GVA_LOG.Error("ChatModel未初始化，无法生成响应")
		return "", fmt.Errorf("ChatModel未初始化")
	}

	// 验证输入
	if len(messages) == 0 {
		global.GVA_LOG.Warn("消息列表为空")
		return "", fmt.Errorf("消息列表不能为空")
	}

	// 记录请求开始
	global.GVA_LOG.Debug("开始生成带上下文的聊天响应",
		zap.Int("message_count", len(messages)),
		zap.String("model", s.config.Model))

	// 调用模型生成响应
	response, err := s.chatModel.Generate(ctx, messages)
	if err != nil {
		global.GVA_LOG.Error("生成带上下文的响应失败",
			zap.Error(err),
			zap.Int("message_count", len(messages)),
			zap.String("model", s.config.Model))
		return "", fmt.Errorf("failed to generate response with context: %w", err)
	}

	// 提取响应内容
	responseContent := ""
	if response != nil && response.Content != "" {
		responseContent = response.Content
	}

	// 记录成功响应
	global.GVA_LOG.Debug("成功生成带上下文的聊天响应",
		zap.Int("message_count", len(messages)),
		zap.Int("response_length", len(responseContent)))

	return responseContent, nil
}

// StreamResponse 流式生成聊天响应
func (s *EinoService) StreamResponse(ctx context.Context, userMessage string) (<-chan StreamChunk, error) {
	if s.chatModel == nil {
		global.GVA_LOG.Error("ChatModel未初始化，无法生成流式响应")
		return nil, fmt.Errorf("ChatModel未初始化")
	}

	// 验证输入
	if userMessage == "" {
		global.GVA_LOG.Warn("用户消息为空")
		return nil, fmt.Errorf("用户消息不能为空")
	}

	// 检查流式响应是否启用
	if !s.config.StreamEnabled {
		global.GVA_LOG.Debug("流式响应未启用，使用普通响应")
		// 如果流式响应未启用，返回单个完整响应
		response, err := s.GenerateResponse(ctx, userMessage)
		if err != nil {
			return nil, err
		}

		resultChan := make(chan StreamChunk, 1)
		go func() {
			defer close(resultChan)
			resultChan <- StreamChunk{
				Type:     "complete",
				Content:  response,
				Sequence: 1,
				IsFinal:  true,
			}
		}()
		return resultChan, nil
	}

	// 记录请求开始
	global.GVA_LOG.Debug("开始生成流式聊天响应",
		zap.String("user_message", userMessage),
		zap.String("model", s.config.Model))

	// 构建Eino消息格式
	messages := []*schema.Message{
		{
			Role:    schema.User,
			Content: userMessage,
		},
	}

	// 调用模型流式生成响应
	streamReader, err := s.chatModel.Stream(ctx, messages)
	if err != nil {
		global.GVA_LOG.Error("创建流式响应失败",
			zap.Error(err),
			zap.String("user_message", userMessage),
			zap.String("model", s.config.Model))
		return nil, fmt.Errorf("failed to create stream response: %w", err)
	}

	// 创建结果通道
	resultChan := make(chan StreamChunk, s.config.StreamBufferSize)

	// 启动goroutine处理流式响应
	go s.processStreamResponse(ctx, streamReader, resultChan, userMessage)

	return resultChan, nil
}

// StreamResponseWithContext 使用上下文生成流式聊天响应
func (s *EinoService) StreamResponseWithContext(ctx context.Context, messages []*schema.Message) (<-chan StreamChunk, error) {
	if s.chatModel == nil {
		global.GVA_LOG.Error("ChatModel未初始化，无法生成流式响应")
		return nil, fmt.Errorf("ChatModel未初始化")
	}

	// 验证输入
	if len(messages) == 0 {
		global.GVA_LOG.Warn("消息列表为空")
		return nil, fmt.Errorf("消息列表不能为空")
	}

	// 检查流式响应是否启用
	if !s.config.StreamEnabled {
		global.GVA_LOG.Debug("流式响应未启用，使用普通响应")
		// 如果流式响应未启用，返回单个完整响应
		response, err := s.GenerateResponseWithContext(ctx, messages)
		if err != nil {
			return nil, err
		}

		resultChan := make(chan StreamChunk, 1)
		go func() {
			defer close(resultChan)
			resultChan <- StreamChunk{
				Type:     "complete",
				Content:  response,
				Sequence: 1,
				IsFinal:  true,
			}
		}()
		return resultChan, nil
	}

	// 记录请求开始
	global.GVA_LOG.Debug("开始生成带上下文的流式聊天响应",
		zap.Int("message_count", len(messages)),
		zap.String("model", s.config.Model))

	// 调用模型流式生成响应
	streamReader, err := s.chatModel.Stream(ctx, messages)
	if err != nil {
		global.GVA_LOG.Error("创建带上下文的流式响应失败",
			zap.Error(err),
			zap.Int("message_count", len(messages)),
			zap.String("model", s.config.Model))
		return nil, fmt.Errorf("failed to create stream response with context: %w", err)
	}

	// 创建结果通道
	resultChan := make(chan StreamChunk, s.config.StreamBufferSize)

	// 启动goroutine处理流式响应
	go s.processStreamResponseWithContext(ctx, streamReader, resultChan, len(messages))

	return resultChan, nil
}

// processStreamResponseWithContext 处理带上下文的流式响应
func (s *EinoService) processStreamResponseWithContext(ctx context.Context, streamReader *schema.StreamReader[*schema.Message], resultChan chan<- StreamChunk, messageCount int) {
	defer close(resultChan)

	sequence := 1
	var fullContent string

	// 设置流式响应超时
	streamCtx, cancel := context.WithTimeout(ctx, s.config.StreamTimeout)
	defer cancel()

	for {
		select {
		case <-streamCtx.Done():
			global.GVA_LOG.Warn("带上下文的流式响应超时",
				zap.Int("message_count", messageCount),
				zap.Duration("timeout", s.config.StreamTimeout))
			resultChan <- StreamChunk{
				Type:     "error",
				Content:  "",
				Sequence: sequence,
				IsFinal:  true,
				Error:    "流式响应超时",
			}
			return
		default:
		}

		// 读取流式数据
		message, err := streamReader.Recv()
		if err != nil {
			if err.Error() == "EOF" || err.Error() == "stream closed" {
				// 流结束，发送最终消息
				global.GVA_LOG.Debug("带上下文的流式响应完成",
					zap.Int("message_count", messageCount),
					zap.Int("total_chunks", sequence-1),
					zap.Int("content_length", len(fullContent)))

				resultChan <- StreamChunk{
					Type:     "complete",
					Content:  fullContent,
					Sequence: sequence,
					IsFinal:  true,
				}
				return
			}

			// 其他错误
			global.GVA_LOG.Error("带上下文的流式响应读取错误",
				zap.Error(err),
				zap.Int("message_count", messageCount),
				zap.Int("sequence", sequence))

			resultChan <- StreamChunk{
				Type:     "error",
				Content:  "",
				Sequence: sequence,
				IsFinal:  true,
				Error:    err.Error(),
			}
			return
		}

		if message != nil && message.Content != "" {
			fullContent += message.Content

			// 发送部分响应
			chunk := StreamChunk{
				Type:     "partial",
				Content:  message.Content,
				Sequence: sequence,
				IsFinal:  false,
			}

			select {
			case resultChan <- chunk:
				global.GVA_LOG.Debug("发送带上下文的流式响应块",
					zap.Int("sequence", sequence),
					zap.Int("content_length", len(message.Content)))
			case <-streamCtx.Done():
				return
			}

			sequence++

			// 控制发送间隔
			if s.config.StreamInterval > 0 {
				select {
				case <-time.After(s.config.StreamInterval):
				case <-streamCtx.Done():
					return
				}
			}
		}
	}
}

// StreamChunk 流式响应数据块
type StreamChunk struct {
	Type     string `json:"type"`            // "partial" 或 "complete"
	Content  string `json:"content"`         // 文本内容
	Sequence int    `json:"sequence"`        // 序列号
	IsFinal  bool   `json:"is_final"`        // 是否为最后一个块
	Error    string `json:"error,omitempty"` // 错误信息（如果有）
}

// processStreamResponse 处理流式响应
func (s *EinoService) processStreamResponse(ctx context.Context, streamReader *schema.StreamReader[*schema.Message], resultChan chan<- StreamChunk, userMessage string) {
	defer close(resultChan)

	sequence := 1
	var fullContent string

	// 设置流式响应超时
	streamCtx, cancel := context.WithTimeout(ctx, s.config.StreamTimeout)
	defer cancel()

	for {
		select {
		case <-streamCtx.Done():
			global.GVA_LOG.Warn("流式响应超时",
				zap.String("user_message", userMessage),
				zap.Duration("timeout", s.config.StreamTimeout))
			resultChan <- StreamChunk{
				Type:     "error",
				Content:  "",
				Sequence: sequence,
				IsFinal:  true,
				Error:    "流式响应超时",
			}
			return
		default:
		}

		// 读取流式数据
		message, err := streamReader.Recv()
		if err != nil {
			if err.Error() == "EOF" || err.Error() == "stream closed" {
				// 流结束，发送最终消息
				global.GVA_LOG.Debug("流式响应完成",
					zap.String("user_message", userMessage),
					zap.Int("total_chunks", sequence-1),
					zap.Int("content_length", len(fullContent)))

				resultChan <- StreamChunk{
					Type:     "complete",
					Content:  fullContent,
					Sequence: sequence,
					IsFinal:  true,
				}
				return
			}

			// 其他错误
			global.GVA_LOG.Error("流式响应读取错误",
				zap.Error(err),
				zap.String("user_message", userMessage),
				zap.Int("sequence", sequence))

			resultChan <- StreamChunk{
				Type:     "error",
				Content:  "",
				Sequence: sequence,
				IsFinal:  true,
				Error:    err.Error(),
			}
			return
		}

		if message != nil && message.Content != "" {
			fullContent += message.Content

			// 发送部分响应
			chunk := StreamChunk{
				Type:     "partial",
				Content:  message.Content,
				Sequence: sequence,
				IsFinal:  false,
			}

			select {
			case resultChan <- chunk:
				global.GVA_LOG.Debug("发送流式响应块",
					zap.Int("sequence", sequence),
					zap.Int("content_length", len(message.Content)))
			case <-streamCtx.Done():
				return
			}

			sequence++

			// 控制发送间隔
			if s.config.StreamInterval > 0 {
				select {
				case <-time.After(s.config.StreamInterval):
				case <-streamCtx.Done():
					return
				}
			}
		}
	}
}

// 全局Eino服务实例
var einoService *EinoService

// GetEinoService 获取全局Eino服务实例
func GetEinoService() *EinoService {
	if einoService == nil {
		einoService = NewEinoService()
	}
	return einoService
}

// InitializeEinoService 初始化全局Eino服务
func InitializeEinoService(ctx context.Context) error {
	service := GetEinoService()
	return service.InitializeChatModel(ctx)
}
