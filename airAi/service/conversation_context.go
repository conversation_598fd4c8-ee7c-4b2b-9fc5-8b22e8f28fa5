package service

import (
	"sync"
	"time"

	"airAi/common/requst"
	"airAi/global"

	"github.com/cloudwego/eino/schema"
	"go.uber.org/zap"
)

// ConversationContext 对话上下文结构
type ConversationContext struct {
	ID          string            `json:"id"`           // 对话ID
	Messages    []*schema.Message `json:"messages"`     // 消息历史
	LastUpdated time.Time         `json:"last_updated"` // 最后更新时间
	MaxHistory  int               `json:"max_history"`  // 最大历史消息数量
}

// ConversationManager 对话管理器
type ConversationManager struct {
	conversations map[string]*ConversationContext
	mutex         sync.RWMutex
	cleanupTicker *time.Ticker
}

var (
	conversationManager *ConversationManager
	once                sync.Once
)

// GetConversationManager 获取对话管理器单例
func GetConversationManager() *ConversationManager {
	once.Do(func() {
		conversationManager = &ConversationManager{
			conversations: make(map[string]*ConversationContext),
			cleanupTicker: time.NewTicker(30 * time.Minute), // 每30分钟清理一次
		}

		// 启动清理goroutine
		go conversationManager.startCleanup()

		global.GVA_LOG.Info("对话管理器初始化成功")
	})
	return conversationManager
}

// startCleanup 启动清理过期对话的goroutine
func (cm *ConversationManager) startCleanup() {
	for range cm.cleanupTicker.C {
		cm.cleanupExpiredConversations()
	}
}

// cleanupExpiredConversations 清理过期对话（超过2小时未活动）
func (cm *ConversationManager) cleanupExpiredConversations() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	expireTime := time.Now().Add(-2 * time.Hour)
	var expiredIDs []string

	for id, ctx := range cm.conversations {
		if ctx.LastUpdated.Before(expireTime) {
			expiredIDs = append(expiredIDs, id)
		}
	}

	for _, id := range expiredIDs {
		delete(cm.conversations, id)
	}

	if len(expiredIDs) > 0 {
		global.GVA_LOG.Info("清理过期对话",
			zap.Int("expired_count", len(expiredIDs)),
			zap.Int("remaining_count", len(cm.conversations)))
	}
}

// GetOrCreateConversation 获取或创建对话上下文
func (cm *ConversationManager) GetOrCreateConversation(conversationID string, maxHistory int) *ConversationContext {
	if conversationID == "" {
		// 如果没有提供对话ID，创建一个新的
		conversationID = generateConversationID()
	}

	if maxHistory <= 0 {
		maxHistory = 10 // 默认保留10条历史消息
	}

	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	ctx, exists := cm.conversations[conversationID]
	if !exists {
		ctx = &ConversationContext{
			ID:          conversationID,
			Messages:    make([]*schema.Message, 0),
			LastUpdated: time.Now(),
			MaxHistory:  maxHistory,
		}
		cm.conversations[conversationID] = ctx

		global.GVA_LOG.Debug("创建新对话上下文",
			zap.String("conversation_id", conversationID),
			zap.Int("max_history", maxHistory))
	} else {
		ctx.LastUpdated = time.Now()
	}

	return ctx
}

// AddMessage 添加消息到对话上下文
func (cm *ConversationManager) AddMessage(conversationID string, role string, content string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	ctx, exists := cm.conversations[conversationID]
	if !exists {
		global.GVA_LOG.Warn("尝试向不存在的对话添加消息",
			zap.String("conversation_id", conversationID))
		return
	}

	// 创建新消息
	var schemaRole schema.RoleType
	switch role {
	case "user":
		schemaRole = schema.User
	case "assistant":
		schemaRole = schema.Assistant
	default:
		schemaRole = schema.User
	}

	message := &schema.Message{
		Role:    schemaRole,
		Content: content,
	}

	ctx.Messages = append(ctx.Messages, message)
	ctx.LastUpdated = time.Now()

	// 如果超过最大历史数量，删除最旧的消息（保留用户-助手对话的完整性）
	if len(ctx.Messages) > ctx.MaxHistory*2 { // *2 因为每轮对话包含用户和助手两条消息
		// 删除最旧的一对消息
		if len(ctx.Messages) >= 2 {
			ctx.Messages = ctx.Messages[2:]
		}
	}

	global.GVA_LOG.Debug("添加消息到对话上下文",
		zap.String("conversation_id", conversationID),
		zap.String("role", role),
		zap.Int("content_length", len(content)),
		zap.Int("total_messages", len(ctx.Messages)))
}

// GetMessages 获取对话的所有消息
func (cm *ConversationManager) GetMessages(conversationID string) []*schema.Message {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	ctx, exists := cm.conversations[conversationID]
	if !exists {
		return make([]*schema.Message, 0)
	}

	// 返回消息的副本，避免并发修改
	messages := make([]*schema.Message, len(ctx.Messages))
	copy(messages, ctx.Messages)

	return messages
}

// BuildContextFromRequest 从请求构建对话上下文
func (cm *ConversationManager) BuildContextFromRequest(req *requst.ChatRequest) ([]*schema.Message, string) {
	var messages []*schema.Message
	conversationID := req.ConversationID

	// 如果请求中包含上下文，使用请求中的上下文
	if len(req.Context) > 0 {
		for _, msg := range req.Context {
			var role schema.RoleType
			switch msg.Role {
			case "user":
				role = schema.User
			case "assistant":
				role = schema.Assistant
			default:
				role = schema.User
			}

			messages = append(messages, &schema.Message{
				Role:    role,
				Content: msg.Content,
			})
		}
	} else if conversationID != "" {
		// 如果有对话ID，从管理器中获取历史消息
		messages = cm.GetMessages(conversationID)
	}

	// 添加当前用户消息
	messages = append(messages, &schema.Message{
		Role:    schema.User,
		Content: req.Text,
	})

	// 如果没有对话ID，创建一个新的
	if conversationID == "" {
		ctx := cm.GetOrCreateConversation("", req.MaxHistory)
		conversationID = ctx.ID
	}

	return messages, conversationID
}

// generateConversationID 生成对话ID
func generateConversationID() string {
	return time.Now().Format("20060102150405") + "_" + randomString(8)
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
