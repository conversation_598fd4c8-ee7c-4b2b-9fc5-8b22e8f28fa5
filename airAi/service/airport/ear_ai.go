package airport

import (
	"airAi/global"
	"airAi/other_api/xunfei"
	"context"
	"errors"
	"io"
)

type UserService struct {
}

func (s *UserService) Chat() {

}

var (
	api   = "/v2/tts"
	rtApi = "/v2/iat"
)

func (s *UserService) RealTime(audio io.Reader) (str string, err error) {
	host := global.GVA_CONFIG.XunfeiConfig.WS + rtApi
	cli := xunfei.NewClient(xunfei.Config{
		Host:      host,
		AppID:     global.GVA_CONFIG.XunfeiConfig.AppID,
		APIKey:    global.GVA_CONFIG.XunfeiConfig.APIKey,
		APISecret: global.GVA_CONFIG.XunfeiConfig.APISecret,
		FrameSize: 0,
	})
	_, err = cli.ConnectWebSocket()
	if err != nil {
		return
	}
	rsp, err := cli.Recognize(context.Background(), audio, "")
	if err != nil {
		return
	}
	if len(rsp) <= 0 {
		err = errors.New("no dataset")
		return
	}
	return rsp[0].Text, nil
}

func (s *UserService) TextToSpeech(text string) (audio []byte, err error) {
	host := global.GVA_CONFIG.XunfeiConfig.WS + api
	client := xunfei.NewClient(xunfei.Config{
		Host:      host,
		AppID:     global.GVA_CONFIG.XunfeiConfig.AppID,
		APIKey:    global.GVA_CONFIG.XunfeiConfig.APIKey,
		APISecret: global.GVA_CONFIG.XunfeiConfig.APISecret,
		FrameSize: 0,
	})
	return client.TextToSpeech(text)

}
