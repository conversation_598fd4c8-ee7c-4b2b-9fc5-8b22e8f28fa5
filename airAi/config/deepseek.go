package config

import "time"

// DeepSeek DeepSeek配置结构
type DeepSeek struct {
	APIKey      string        `mapstructure:"api-key" json:"api-key" yaml:"api-key"`             // DeepSeek API密钥
	BaseURL     string        `mapstructure:"base-url" json:"base-url" yaml:"base-url"`          // API基础URL
	Model       string        `mapstructure:"model" json:"model" yaml:"model"`                   // 模型名称
	MaxTokens   int           `mapstructure:"max-tokens" json:"max-tokens" yaml:"max-tokens"`    // 最大令牌数
	Temperature float32       `mapstructure:"temperature" json:"temperature" yaml:"temperature"` // 温度参数
	TopP        float32       `mapstructure:"top-p" json:"top-p" yaml:"top-p"`                   // TopP参数
	Timeout     time.Duration `mapstructure:"timeout" json:"timeout" yaml:"timeout"`             // 请求超时时间

	// 流式响应配置
	StreamEnabled    bool          `mapstructure:"stream-enabled" json:"stream-enabled" yaml:"stream-enabled"`             // 是否启用流式响应
	StreamBufferSize int           `mapstructure:"stream-buffer-size" json:"stream-buffer-size" yaml:"stream-buffer-size"` // 流式缓冲区大小
	StreamInterval   time.Duration `mapstructure:"stream-interval" json:"stream-interval" yaml:"stream-interval"`          // 流式发送间隔
	StreamTimeout    time.Duration `mapstructure:"stream-timeout" json:"stream-timeout" yaml:"stream-timeout"`             // 流式响应超时时间
}
